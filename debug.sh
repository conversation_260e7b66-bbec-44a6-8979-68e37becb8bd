#!/bin/bash

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    log_error "无法找到SSH工具库 lib/ssh_utils.sh"
    exit 1
fi

node_config=$(get_device_config "n1")
read -r host user password port backup_user backup_password <<< "$node_config"
./modules/system_check.sh "$target_node" "$host" "$user" "$password" "$port"