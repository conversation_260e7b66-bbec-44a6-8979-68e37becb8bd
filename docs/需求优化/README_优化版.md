# AFW3000防火墙设备检测脚本（优化版）

## 概述

基于实际需求清单设计的AFW3000防火墙设备检测脚本集合，支持N1(ABT8410)、N2(E2000Q)、N3(E2000Q)三个节点的全面检测。

## 支持的设备和平台

### 设备型号
- **N1节点**: ABT8410 (*************)
- **N2节点**: E2000Q (*************)  
- **N3节点**: E2000Q (*************)

### 支持的操作系统平台
- CentOS 7.9
- UOS 20 1050
- <PERSON><PERSON>in v10 sp1

## 功能特性

- **网络检测**：网络联通性、端口可达性、延迟监控、网络接口状态
- **Web服务检测**：Web响应、登录验证、服务状态、SSL证书检查
- **信息获取**：缓存信息、硬件信息、系统日志
- **系统平台检测**：操作系统版本、内核版本、系统架构、运行时间、主机信息
- **硬件信息检测**：CPU、内存、硬盘、网口、安全芯片、硬件状态监控
- **软件版本检测**：防火墙软件、N2集群、同步客户端、RabbitMQ、DNS服务、数据库
- **统一调度**：支持多设备并发检测，JSON格式结果输出

## 脚本列表

| 脚本名称 | 功能描述 | 状态 |
|----------|----------|------|
| `check_afw3000_network.sh` | 网络检测 | ✅ 新增 |
| `check_afw3000_web.sh` | Web服务检测 | ✅ 已有 |
| `check_afw3000_platform.sh` | 系统平台检测 | ✅ 新增 |
| `check_afw3000_hardware.sh` | 硬件信息检测 | ✅ 优化 |
| `check_afw3000_software.sh` | 软件版本检测 | ✅ 优化 |
| `check_afw3000_all_optimized.sh` | 统一调度脚本（优化版） | ✅ 新增 |

## 快速开始

### 1. 环境准备

```bash
# 安装必要工具
sudo yum install -y openssh-clients curl jq netcat-openbsd
# 或者 Ubuntu/Debian
sudo apt-get install -y openssh-client curl jq netcat-openbsd

# 克隆或下载脚本
git clone <repository> afw3000-check
cd afw3000-check
```

### 2. 配置设备信息

编辑配置文件 `config/devices.conf`：

```bash
# N1节点配置（ABT8410）
N1_HOST="*************"
N1_TYPE="ABT8410"
N1_USER="admin"
N1_KEY="/path/to/n1.key"

# N2节点配置（E2000Q）
N2_HOST="*************"
N2_TYPE="E2000Q"
N2_USER="admin"
N2_KEY="/path/to/n2.key"

# N3节点配置（E2000Q）
N3_HOST="*************"
N3_TYPE="E2000Q"
N3_USER="admin"
N3_KEY="/path/to/n3.key"
```

### 3. 执行检测

```bash
# 单项检测
./check_afw3000_network.sh ************* admin /path/to/key
./check_afw3000_platform.sh ************* admin /path/to/key
./check_afw3000_software.sh ************* admin /path/to/key

# 全面检测（推荐）
./check_afw3000_all_optimized.sh
```

## 详细使用说明

### 网络检测脚本

```bash
# 基本用法
./check_afw3000_network.sh <host> [user] [key_file]

# 示例
./check_afw3000_network.sh ************* admin /root/.ssh/id_rsa

# 检测项目
- 网络联通性（ping测试）
- 端口可达性（22,80,443,8080）
- 网络延迟监控
- 网络接口状态（需要SSH）
```

### 系统平台检测脚本

```bash
# 基本用法
./check_afw3000_platform.sh <host> <user> [key_file]

# 示例
./check_afw3000_platform.sh ************* admin /root/.ssh/id_rsa

# 检测项目
- 操作系统发行版（centos7.9, uos20 1050, kylin v10 sp1）
- 内核版本
- 系统架构（x86_64, aarch64）
- 系统运行时间和负载
- 主机名和域名信息
```

### 软件版本检测脚本

```bash
# 基本用法
./check_afw3000_software.sh <host> <user> [key_file]

# 示例
./check_afw3000_software.sh ************* admin /root/.ssh/id_rsa

# 检测项目
- 防火墙软件版本
- N2集群软件状态
- 系统同步客户端
- RabbitMQ消息队列
- 时间同步服务（NTP/Chrony）
- DNS服务状态
- 数据库软件版本
```

### 统一调度脚本

```bash
# 执行全面检测
./check_afw3000_all_optimized.sh

# 输出文件
- results/afw3000_check_result_YYYYMMDD_HHMMSS.json  # 详细JSON结果
- results/afw3000_check_summary_YYYYMMDD_HHMMSS.txt # 文本摘要报告
```

## 配置文件说明

### devices.conf - 设备配置
```bash
# 设备连接信息
N1_HOST="*************"     # 设备IP地址
N1_TYPE="ABT8410"           # 设备型号
N1_USER="admin"             # SSH用户名
N1_KEY="/path/to/key"       # SSH私钥文件路径
```

### detection_items.conf - 检测项配置
```bash
# 网络检测配置
CHECK_PORTS="22,80,443,8080"  # 检测的端口列表
PING_TIMEOUT=5                # ping超时时间
LATENCY_THRESHOLD=100         # 延迟告警阈值(ms)

# 系统资源阈值
CPU_THRESHOLD=80              # CPU使用率阈值(%)
MEMORY_THRESHOLD=85           # 内存使用率阈值(%)
DISK_THRESHOLD=80             # 磁盘使用率阈值(%)
```

### thresholds.conf - 告警阈值配置
```bash
# 网络阈值
NETWORK_LATENCY_WARNING=50    # 网络延迟警告阈值(ms)
NETWORK_LATENCY_CRITICAL=100  # 网络延迟严重阈值(ms)

# 系统资源阈值
CPU_WARNING=70               # CPU使用率警告阈值(%)
CPU_CRITICAL=85              # CPU使用率严重阈值(%)
```

## 结果输出格式

### JSON格式结果
```json
{
    "check_info": {
        "timestamp": "2025-07-22 10:30:00",
        "script_version": "2.0",
        "check_type": "comprehensive"
    },
    "devices": [
        {
            "device_name": "N1",
            "device_type": "ABT8410",
            "device_host": "*************",
            "status": "success",
            "check_summary": {
                "total_checks": 5,
                "successful_checks": 5,
                "success_rate": "100%"
            },
            "results": {
                "network_check": "success",
                "web_check": "success",
                "platform_check": "success",
                "hardware_check": "success",
                "software_check": "success"
            }
        }
    ],
    "summary": {
        "total_devices": 3,
        "successful_devices": 3,
        "failed_devices": 0,
        "overall_status": "0"
    }
}
```

### 文本格式摘要
```
AFW3000防火墙设备检测摘要报告
=====================================

检测时间: 2025-07-22 10:30:00
检测脚本版本: 2.0 (优化版)

设备检测概况:
- 总设备数: 3
- 检测成功: 3
- 检测失败: 0
- 成功率: 100%

设备详细状态:
- N1 (ABT8410): success - 100%
- N2 (E2000Q): success - 100%
- N3 (E2000Q): success - 100%

总体状态: 所有设备检测通过 ✓
```

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查SSH密钥权限：`chmod 600 /path/to/key`
   - 确认用户名和密钥路径正确
   - 测试手动SSH连接

2. **命令未找到**
   - 安装缺失的工具：`jq`, `netcat`, `curl`
   - 检查PATH环境变量

3. **权限不足**
   - 确保SSH用户有足够权限执行系统命令
   - 某些硬件检测可能需要root权限

4. **网络超时**
   - 调整配置文件中的超时设置
   - 检查网络连通性

### 调试模式

```bash
# 启用调试输出
export DEBUG=1
./check_afw3000_all_optimized.sh

# 查看详细日志
tail -f logs/afw3000_check.log
```

## 定时任务配置

```bash
# 添加到crontab，每小时执行一次
0 * * * * /path/to/afw3000-check/check_afw3000_all_optimized.sh

# 每天早上8点执行
0 8 * * * /path/to/afw3000-check/check_afw3000_all_optimized.sh
```

## 版本历史

- **v2.0** (2025-07-22): 基于实际需求优化，支持多设备类型检测
- **v1.0** (初始版本): 基础Web服务检测功能

## 技术支持

如有问题或建议，请联系技术支持团队。

---

**注意**: 本脚本基于实际AFW3000防火墙设备检测需求设计，请根据实际环境调整配置参数。
