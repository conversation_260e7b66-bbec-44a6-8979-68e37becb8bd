# AFW3000防火墙设备检测脚本需求搜集表格

## 基本信息

| 项目 | 内容 |
|------|------|
| 填表人姓名 | |
| 部门/组织 | |
| 联系方式 | |
| 填表日期 | |
| 预期完成时间 | |

## 设备环境信息

### 设备基本配置
| 配置项 | 详细信息 | 备注 |
|--------|----------|------|
| 设备型号 | AFW3000 | |
| 节点配置 | □ 单节点 □ 双节点 □ 三节点 | 请勾选实际配置 |
| 网络环境 | □ 内网 □ 外网 | |

### 节点详细信息
| 节点 | 设备型号 | IP地址 | SSH端口 | 用户名 | 当前状态 | 备注 |
|------|----------|--------|---------|--------|----------|------|
| N1 | ABT8410 | ************* | 22 | | □ 正常 □ 异常 | Web检测已实现 |
| N2 | E2000Q | ************* | 22 | | □ 正常 □ 异常 | 需要新增功能 |
| N3 | E2000Q | ************* | 22 | | □ 正常 □ 异常 | 需要新增功能 |

## 检测需求详细说明

### 1. 网络检测需求（所有节点）

#### 1.1 网络连通性检测
| 检测项 | 是否需要 | 具体要求 |
|--------|----------|----------|
| 网络联通性 | □ 是 □ 否 | ping测试，超时时间：___秒 |
| 端口可达性 | □ 是 □ 否 | 检测关键端口：22,80,443,8080等 |
| 网络延迟监控 | □ 是 □ 否 | 延迟阈值：___ms |

### 2. Web服务检测需求（所有节点）

#### 2.1 基础Web服务检测
| 检测项 | 是否需要 | 具体要求 |
|--------|----------|----------|
| Web服务响应 | □ 是 □ 否 | HTTP状态码检查 |
| 服务器响应 | □ 是 □ 否 | 响应时间阈值：___秒 |
| 登录验证 | □ 是 □ 否 | 自动登录测试 |
| 登录状态 | □ 是 □ 否 | 会话保持检查 |

#### 2.2 高级Web服务检测
| 检测项 | 是否需要 | 具体要求 |
|--------|----------|----------|
| SSL证书检查 | □ 是 □ 否 | 证书有效期检查 |
| Web界面功能 | □ 是 □ 否 | 关键功能页面访问测试 |

### 3. 信息获取需求（所有节点）

#### 3.1 基础信息获取
| 信息类型 | 是否需要 | 具体要求 |
|----------|----------|----------|
| 缓存信息 | □ 是 □ 否 | 缓存使用情况 |
| 硬件信息 | □ 是 □ 否 | CPU、内存、硬盘信息 |
| 系统日志 | □ 是 □ 否 | 关键日志信息 |

### 4. 系统平台信息检测需求（所有节点）

#### 4.1 操作系统信息
| 信息类型 | 是否需要 | 具体要求 |
|----------|----------|----------|
| 操作系统版本 | □ 是 □ 否 | CentOS 7.9, Ubuntu 20.04, Kylin v10 sp1等 |
| 内核版本 | □ 是 □ 否 | 内核版本信息 |
| 系统架构 | □ 是 □ 否 | x86_64, ARM等 |

### 5. 系统信息获取需求（所有节点）

#### 5.1 系统资源监控
| 信息类型 | 是否需要 | 告警阈值 | 备注 |
|----------|----------|----------|------|
| CPU使用率 | □ 是 □ 否 | ___%  | 超过阈值时告警 |
| 内存使用率 | □ 是 □ 否 | ___%  | 超过阈值时告警 |
| 磁盘使用率 | □ 是 □ 否 | ___%  | 超过阈值时告警 |
| 系统负载 | □ 是 □ 否 | ___ | 1分钟平均负载 |
| 发行版本 | □ 是 □ 否 | | 操作系统发行版信息 |
| 内核版本 | □ 是 □ 否 | | 内核版本信息 |
| 运行时间 | □ 是 □ 否 | | 系统运行时间 |
| 主机 | □ 是 □ 否 | | 主机名信息 |

### 6. 硬件信息获取需求（所有节点）

#### 6.1 基础硬件信息
| 硬件类型 | 是否需要 | 具体要求 |
|----------|----------|----------|
| CPU信息 | □ 是 □ 否 | CPU型号、核心数、频率 |
| 内存信息 | □ 是 □ 否 | 内存总容量、类型 |
| 硬盘信息 | □ 是 □ 否 | 硬盘型号、容量、分区 |
| 网口信息 | □ 是 □ 否 | 网络接口类型、状态 |

#### 6.2 硬件状态检测
| 监控项 | 是否需要 | 告警阈值 | 备注 |
|--------|----------|----------|------|
| 硬盘健康状态 | □ 是 □ 否 | | 通过smartctl检查SMART信息 |
| CPU风扇状态 | □ 是 □ 否 | | 检查风扇转速和状态 |
| 安全芯片 | □ 是 □ 否 | | 安全芯片状态检查 |
| 安全加固 | □ 是 □ 否 | | 系统安全加固状态 |
| 软件数据库 | □ 是 □ 否 | | 软件数据库完整性检查 |
| 网络异常检测引擎 | □ 是 □ 否 | | 网络异常检测功能状态 |

### 7. 软件版本获取需求（所有节点）

#### 7.1 防火墙软件版本
| 软件类型 | 是否需要 | 具体要求 |
|----------|----------|----------|
| 软件防火墙 | □ 是 □ 否 | 防火墙软件版本信息 |
| 主要N2集群 | □ 是 □ 否 | 集群软件版本 |
| 系统同步客户端 | □ 是 □ 否 | 同步客户端版本 |

#### 7.2 系统软件版本
| 软件类型 | 是否需要 | 具体要求 |
|----------|----------|----------|
| 消息队列软件(rabbitmq) | □ 是 □ 否 | RabbitMQ版本信息 |
| 时间同步 | □ 是 □ 否 | NTP/Chrony同步状态 |
| DNS建立服务 | □ 是 □ 否 | DNS服务状态和版本 |

#### 7.3 数据库和中间件
| 软件类型 | 是否需要 | 具体要求 |
|----------|----------|----------|
| 数据库软件 | □ 是 □ 否 | 数据库版本和状态 |
| ysdomn | □ 是 □ 否 | ysdomn组件版本 |
| 消息队列软件(bccrl) | □ 是 □ 否 | bccrl消息队列版本 |
| 消息队列同步客户端 | □ 是 □ 否 | 同步客户端版本 |

## 脚本实现要求

### 1. 检测方式
| 检测方式 | 是否使用 | 备注 |
|----------|----------|------|
| SSH远程检测 | □ 是 □ 否 | 主要检测方式 |
| 本地命令执行 | □ 是 □ 否 | 在设备本地运行脚本 |

### 2. 脚本技术要求
| 技术要求 | 具体要求 |
|----------|----------|
| 脚本语言 | Shell脚本（基于现有check_afw3000_web.sh） |
| 兼容性 | 支持的操作系统：___ |
| 依赖工具 | 需要的系统工具：___ |
| 执行权限 | □ root权限 □ 普通用户权限 |

### 3. 输出要求
| 输出类型 | 是否需要 | 格式要求 |
|----------|----------|----------|
| 控制台输出 | □ 是 □ 否 | 彩色日志、时间戳 |
| 日志文件 | □ 是 □ 否 | 日志文件路径：___ |
| 结果文件 | □ 是 □ 否 | □ JSON □ CSV □ TXT |
| 邮件通知 | □ 是 □ 否 | 告警邮件地址：___ |

## 脚本部署和运行要求

### 1. 运行环境
| 环境要求 | 具体配置 |
|----------|----------|
| 运行位置 | □ 管理服务器 □ 防火墙设备本地 |
| 操作系统 | 支持的系统：___ |
| 网络要求 | □ 需要SSH访问 □ 本地执行 |
| 存储空间 | 日志文件存储需求：___MB |

### 2. 定时执行
| 执行方式 | 是否需要 | 具体要求 |
|----------|----------|----------|
| cron定时任务 | □ 是 □ 否 | 执行频率：___ |
| 手动执行 | □ 是 □ 否 | 按需检测 |
| 开机自启动 | □ 是 □ 否 | 系统启动时检测 |

## 安全要求

### 1. 访问安全
| 安全项 | 具体要求 |
|--------|----------|
| SSH密钥认证 | □ 使用密钥 □ 使用密码 |
| 用户权限 | 执行脚本的用户：___ |
| 网络安全 | □ 内网访问 □ VPN访问 |
| 日志安全 | 日志文件权限：___ |

## 性能要求

### 1. 脚本性能
| 性能指标 | 具体要求 |
|----------|----------|
| 单次检测时间 | 不超过___分钟 |
| 资源占用 | CPU使用率不超过___%  |
| 网络带宽 | 检测过程中带宽占用：___ |
| 并发检测 | 是否支持多设备并发：□ 是 □ 否 |

## 维护要求

### 1. 脚本维护
| 维护类型 | 具体要求 |
|----------|----------|
| 脚本更新 | □ 需要版本管理 □ 简单替换 |
| 配置修改 | □ 配置文件 □ 脚本内修改 |
| 日志管理 | 日志保留时间：___天 |
| 故障处理 | 故障通知方式：___ |

### 2. 文档要求
| 文档类型 | 是否需要 |
|----------|----------|
| 使用说明 | □ 是 □ 否 |
| 安装部署文档 | □ 是 □ 否 |
| 故障排查手册 | □ 是 □ 否 |

## 开发时间要求

### 1. 时间计划
| 阶段 | 预期时间 | 备注 |
|------|----------|------|
| 需求确认 | ___天 | |
| 脚本开发 | ___天 | |
| 测试验证 | ___天 | |
| 部署上线 | ___天 | |
| 总计 | ___天 | |

## 特殊要求

### 1. 特殊功能需求
| 功能描述 | 是否需要 | 具体要求 |
|----------|----------|----------|
| 多设备批量检测 | □ 是 □ 否 | 设备列表文件支持 |
| 检测结果对比 | □ 是 □ 否 | 与历史结果对比 |
| 自定义检测项 | □ 是 □ 否 | 支持用户自定义检测 |

### 2. 约束条件
| 约束类型 | 具体约束 |
|----------|----------|
| 技术约束 | |
| 环境约束 | |
| 安全约束 | |

## 需求确认

### 1. 需求优先级排序
请按重要性排序（1-4，1为最重要）：

| 需求类别 | 优先级 |
|----------|--------|
| Web服务检测功能完善 | |
| 系统信息检测 | |
| 硬件信息检测 | |
| 软件版本信息检测 | |

### 2. 补充说明
请在此处添加任何其他重要信息或特殊要求：

_________________________________________________________________
_________________________________________________________________
_________________________________________________________________

---

**表格填写说明：**
1. 请根据实际需求在相应的□中打"√"
2. 在"___"处填写具体的数值或要求
3. 重点关注实际可操作的需求，避免过于复杂的功能
4. 如有疑问，请联系开发人员进行沟通

**提交方式：**
- 电子版：发送至开发人员邮箱

**联系方式：**
- 脚本开发人员：[姓名] [电话] [邮箱]

---
**文档版本：** 2.0（简化版）
**创建日期：** 2025-07-22
**适用范围：** Shell脚本开发
