# AFW3000防火墙设备检测脚本需求搜集指导文档

## 文档目的

本文档帮助用户正确填写《AFW3000_需求搜集表格.md》，为Shell脚本开发提供明确的需求指导。

## 填写前准备

### 1. 环境调研
在填写需求表格前，请先了解：

- **设备信息**：确认AFW3000设备的IP地址、SSH访问信息
- **网络环境**：确认网络连通性和访问权限
- **现有脚本**：了解当前`check_afw3000_web.sh`的使用情况
- **运维需求**：明确日常运维中需要检测的关键指标

## 填写指导

### 1. 设备环境信息

**节点配置说明：**
- **单节点**：只有一个AFW3000设备
- **双节点**：两个设备组成高可用配置
- **三节点**：三个设备的集群配置

**重要信息：**
- 节点1 (*************)：已有Web检测功能
- 节点2/3：需要新增系统、硬件、软件检测功能
- 确保填写正确的SSH端口和用户名

### 2. 检测需求说明

#### 2.1 Web服务检测（基于现有脚本）

**当前功能评估：**
基于现有的`check_afw3000_web.sh`脚本，评估以下功能是否满足需求：
- 网络连通性检测（ping测试）
- HTTPS端口检测（端口443）
- Web服务响应检测（HTTP状态码）
- 登录流程验证
- 错误诊断信息

**新增需求考虑：**
- SSL证书有效期检查
- 响应时间监控
- 登录成功率统计

#### 2.2 系统信息检测

**关键指标：**
- **CPU使用率**：建议阈值80%告警
- **内存使用率**：建议阈值85%告警
- **磁盘使用率**：建议阈值80%告警
- **系统负载**：1分钟平均负载
- **网络接口状态**：检查up/down状态
- **关键进程**：指定需要监控的进程

#### 2.3 硬件信息检测

**基础信息：**
- CPU信息（型号、核心数）
- 内存信息（总容量）
- 存储设备信息
- 网络接口信息

**状态监控：**
- CPU温度（需要lm-sensors）
- 硬盘健康（需要smartctl）
- 风扇状态

#### 2.4 软件版本检测

**版本信息：**
- 操作系统版本和内核版本
- AFW3000软件版本
- 关键软件包版本

**更新状态：**
- 可用系统更新
- 安全补丁状态

### 3. 脚本实现要求

#### 3.1 检测方式选择

**SSH远程检测：**
- 优点：功能强大，可执行各种系统命令
- 缺点：需要SSH访问权限
- 适用：大部分系统和硬件信息检测

**本地执行：**
- 优点：无需网络访问，执行速度快
- 缺点：需要在每台设备上部署脚本
- 适用：设备本地定时检测

#### 3.2 技术要求

**脚本兼容性：**
- 基于现有`check_afw3000_web.sh`脚本扩展
- 支持常见Linux发行版
- 使用标准Shell命令，避免特殊依赖

**安全考虑：**
- SSH密钥认证优于密码认证
- 使用最小权限用户执行
- 敏感信息不在脚本中硬编码

**性能要求：**
- 单次检测时间控制在合理范围内
- 避免对设备性能造成明显影响
- 支持并发检测多个设备

### 4. 输出要求

#### 4.1 输出格式

**控制台输出：**
- 彩色日志便于区分不同级别信息
- 时间戳记录便于问题追踪
- 清晰的成功/失败状态显示

**文件输出：**
- **日志文件**：记录详细的执行过程
- **结果文件**：JSON/CSV格式便于后续处理
- **报告文件**：汇总检测结果

#### 4.2 通知方式

**邮件通知：**
- 适合告警信息通知
- 可包含详细的检测结果

**本地通知：**
- 系统日志记录
- 本地文件存储

### 5. 部署和运行

#### 5.1 运行环境

**管理服务器运行：**
- 在专门的管理服务器上运行脚本
- 通过SSH连接到各个AFW3000设备
- 集中管理和监控

**设备本地运行：**
- 在每台AFW3000设备上部署脚本
- 通过cron定时执行
- 结果可通过网络传输到管理中心

#### 5.2 定时执行

**cron定时任务：**
- 设置合适的执行频率
- 避免在业务高峰期执行
- 错开不同设备的检测时间

## 填写建议

### 1. 需求优先级

**重要原则：**
- 优先考虑影响业务的关键功能
- 重点关注日常运维中最需要的检测项
- 考虑脚本开发的复杂度和时间成本

### 2. 实用性考虑

**具体化需求：**
- 明确具体的阈值和参数
- 避免模糊的描述
- 考虑实际的运维场景

**技术可行性：**
- 基于Shell脚本的能力范围
- 考虑设备的实际环境和权限
- 避免过于复杂的功能需求

## 后续流程

### 1. 需求确认

1. **需求审查**：开发人员审查需求的可行性
2. **需求澄清**：对不明确的需求进行沟通
3. **需求确认**：最终确认开发范围和优先级

### 2. 脚本开发

1. **脚本设计**：基于需求设计脚本结构
2. **功能开发**：按优先级开发各检测功能
3. **测试验证**：在测试环境验证脚本功能
4. **部署上线**：部署到生产环境

### 3. 交付内容

- 检测脚本文件
- 使用说明文档
- 部署配置指导
- 简单的故障排查手册

## 联系方式

如有问题请联系脚本开发人员：
- 技术问题咨询
- 需求澄清确认
- 实现方案讨论

---

**文档版本：** 2.0（简化版）
**创建日期：** 2025-07-22
**适用范围：** Shell脚本开发需求收集
