# AFW3000检测脚本模块接口设计

## 概述

本文档定义AFW3000防火墙设备检测脚本各模块的接口规范，确保模块间的标准化交互和统一的输出格式。

## 模块接口规范

### 1. 通用接口标准

#### 1.1 输入参数规范
```bash
# 所有检测模块统一的输入参数
$1: 设备名称    (n1/n2/n3)
$2: 设备IP地址  (*************/201/202)
$3: SSH用户名   (admin)
$4: SSH密码     (password)
```

#### 1.2 输出格式规范
```bash
# 标准输出格式
[模块名称] 检测项目: 状态 (详细信息)

# 返回值规范
0: 检测成功，所有项目正常
1: 检测失败，存在异常项目
2: 检测错误，无法执行检测
```

#### 1.3 日志输出规范
```bash
# 日志级别和格式
INFO:  [模块名称] 信息内容
WARN:  [模块名称] 警告内容
ERROR: [模块名称] 错误内容
```

## 检测模块详细设计

### 1. 网络检测模块 (modules/network_check.sh)

#### 1.1 模块职责
- 检测SSH连接状态
- 检测关键端口可达性
- 作为后续检测的前置条件

#### 1.2 输出格式示例
```text
[网络检测] 连接状态: 正常
[网络检测] 端口22: 开放
[网络检测] 端口80: 开放
[网络检测] 端口443: 开放
[网络检测] 端口8080: 关闭
```

#### 1.3 检测逻辑
```bash
检测流程：
1. SSH连接测试 (验证认证和连接)
2. 关键端口检测 (22,80,443,8080)
3. 检测依赖：失败则跳过后续检测
4. 结果汇总输出
```

### 2. 系统状态检测模块 (modules/system_check.sh)

#### 2.1 模块职责
- 检测CPU使用率
- 检测内存使用率
- 检测磁盘使用率
- 检测系统负载

#### 2.2 输出格式示例
```text
[系统状态] CPU使用率: 正常 (15%)
[系统状态] 内存使用率: 正常 (45%)
[系统状态] 磁盘使用率: 正常 (60%)
[系统状态] 系统负载: 正常 (0.8)
```

#### 2.3 检测逻辑
```bash
检测流程：
1. 通过SSH获取系统资源信息
2. 解析CPU、内存、磁盘使用率
3. 获取系统负载信息
4. 与阈值比较判断状态
5. 格式化输出结果
```

### 3. 硬件信息检测模块 (modules/hardware_check.sh)

#### 3.1 模块职责
- 获取主板硬件信息
- 获取CPU硬件信息
- 获取内存硬件信息
- 获取硬盘硬件信息
- 获取网卡硬件信息

#### 3.2 输出格式示例
```text
[硬件信息] 主板:
[硬件信息]   型号: ASUS PRIME B450M-A
[硬件信息]   制造商: ASUSTeK COMPUTER INC.
[硬件信息]   BIOS版本: American Megatrends Inc. 2801
[硬件信息] CPU:
[硬件信息]   型号: Intel(R) Core(TM) i7-8700 CPU @ 3.20GHz
[硬件信息]   核心数: 6核12线程
[硬件信息] 内存:
[硬件信息]   容量: 16GB DDR4
[硬件信息] 存储:
[硬件信息]   容量: 500GB SSD + 1TB HDD
[硬件信息] 网口:
[硬件信息]   型号: Intel I219-V (1Gbps)
[硬件信息]   配置: eth0 (*************/24)
```

#### 3.3 检测逻辑
```bash
检测流程：
1. 通过SSH执行硬件信息获取命令
2. 解析主板信息 (dmidecode -t baseboard)
3. 解析BIOS信息 (dmidecode -t bios)
4. 解析CPU信息 (lscpu, /proc/cpuinfo)
5. 解析内存信息 (free, /proc/meminfo)
6. 解析硬盘信息 (lsblk, fdisk)
7. 解析网卡信息 (ip link, lspci)
8. 格式化输出结果
```

### 4. 软件版本检测模块 (modules/software_check.sh)

#### 4.1 模块职责
- 检测操作系统版本
- 检测内核版本
- 检测防火墙软件版本
- 检测关键服务状态

#### 4.2 输出格式示例
```text
[软件版本] 操作系统: CentOS Linux 7.9.2009
[软件版本] 内核版本: 3.10.0-1160.el7.x86_64
[软件版本] 防火墙软件: AFW3000 v2.1.0
[软件版本] SSH服务: 运行中 (OpenSSH 7.4)
[软件版本] Web服务: 运行中 (nginx 1.16.1)
```

#### 4.3 检测逻辑
```bash
检测流程：
1. 获取操作系统版本信息
2. 获取内核版本信息
3. 检测防火墙软件版本
4. 检测关键服务运行状态
5. 格式化输出结果
```

## 公共函数库设计

### 1. 公共函数库 (lib/common_functions.sh)

#### 1.1 日志输出函数
```bash
# 日志输出函数设计
log_info() {
    echo "INFO:  [$(date '+%H:%M:%S')] $1"
}

log_warn() {
    echo "WARN:  [$(date '+%H:%M:%S')] $1" >&2
}

log_error() {
    echo "ERROR: [$(date '+%H:%M:%S')] $1" >&2
}
```

#### 1.2 配置读取函数
```bash
# 配置文件读取函数
load_config() {
    local config_file="$1"
    if [ -f "$config_file" ]; then
        source "$config_file"
        return 0
    else
        log_error "配置文件不存在: $config_file"
        return 1
    fi
}

# 获取设备配置信息
get_device_config() {
    local device_name="$1"
    case "$device_name" in
        "n1")
            echo "$N1_HOST $N1_USER $N1_PASSWORD"
            ;;
        "n2")
            echo "$N2_HOST $N2_USER $N2_PASSWORD"
            ;;
        "n3")
            echo "$N3_HOST $N3_USER $N3_PASSWORD"
            ;;
        *)
            return 1
            ;;
    esac
}
```

#### 1.3 结果处理函数
```bash
# 格式化检测结果
format_result() {
    local module_name="$1"
    local check_item="$2"
    local status="$3"
    local details="$4"
    
    local status_symbol
    if [ "$status" = "success" ]; then
        status_symbol="正常"
    else
        status_symbol="异常"
    fi
    
    echo "[$module_name] $check_item: $status_symbol ($details)"
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=""
    
    for tool in ssh ping curl awk sed; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools="$missing_tools $tool"
        fi
    done
    
    if [ -n "$missing_tools" ]; then
        log_error "缺少必需工具:$missing_tools"
        return 1
    fi
    
    return 0
}
```

### 2. SSH工具函数 (lib/ssh_utils.sh)

#### 2.1 SSH连接函数
```bash
# SSH连接测试
ssh_connect() {
    local host="$1"
    local user="$2"
    local password="$3"
    local timeout="${SSH_TIMEOUT:-10}"

    sshpass -p "$password" ssh -o ConnectTimeout="$timeout" \
        -o StrictHostKeyChecking=no \
        -o BatchMode=yes \
        "$user@$host" \
        "echo 'SSH连接成功'" >/dev/null 2>&1
}

# SSH命令执行
ssh_execute() {
    local host="$1"
    local user="$2"
    local password="$3"
    local command="$4"
    local timeout="${SSH_TIMEOUT:-10}"

    sshpass -p "$password" ssh -o ConnectTimeout="$timeout" \
        -o StrictHostKeyChecking=no \
        -o BatchMode=yes \
        "$user@$host" \
        "$command" 2>/dev/null
}
```

## 主入口脚本设计

### 1. 参数处理逻辑
```bash
# check_afw3000.sh 主要逻辑结构

main() {
    # 1. 参数验证
    validate_params "$@"
    
    # 2. 加载配置
    load_configs
    
    # 3. 检查依赖
    check_dependencies
    
    # 4. 执行检测
    case "$1" in
        "all")
            check_all_devices
            ;;
        "n1"|"n2"|"n3")
            check_single_device "$1"
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
    
    # 5. 生成报告
    generate_report
}
```

### 2. 设备检测流程
```bash
# 单设备检测流程
check_single_device() {
    local device_name="$1"
    local device_config

    # 获取设备配置
    device_config=$(get_device_config "$device_name")
    read -r host user password <<< "$device_config"

    log_info "开始检测设备: $device_name ($host)"

    # 执行各检测模块
    ./modules/network_check.sh "$device_name" "$host" "$user" "$password"
    ./modules/system_check.sh "$device_name" "$host" "$user" "$password"
    ./modules/hardware_check.sh "$device_name" "$host" "$user" "$password"
    ./modules/software_check.sh "$device_name" "$host" "$user" "$password"

    log_info "设备检测完成: $device_name"
}
```

## 报告生成设计

### 1. 报告生成函数
```bash
# 生成检测报告
generate_report() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local report_file="report/afw3000_check_$timestamp.txt"
    
    # 创建报告目录
    mkdir -p "report"
    
    # 生成报告头部
    generate_report_header > "$report_file"
    
    # 添加检测结果
    generate_report_content >> "$report_file"
    
    # 添加报告尾部
    generate_report_footer >> "$report_file"
    
    log_info "检测报告已生成: $report_file"
}
```

### 2. 报告内容结构
```bash
报告结构设计：
├── 报告头部
│   ├── 检测时间
│   ├── 检测范围
│   └── 脚本版本
├── 设备概况
│   ├── 总设备数
│   ├── 检测成功数
│   └── 检测失败数
├── 详细结果
│   ├── 各设备检测结果
│   └── 异常项目详情
└── 报告尾部
    ├── 检测总结
    ├── 处理建议
    └── 生成时间
```

---

**接口设计原则：**
- **标准化**：统一的输入输出格式
- **模块化**：清晰的职责分离
- **可扩展**：便于添加新的检测模块
- **易维护**：简洁的接口设计
- **容错性**：完善的错误处理机制
