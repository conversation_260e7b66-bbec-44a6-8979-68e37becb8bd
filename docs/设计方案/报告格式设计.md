# AFW3000检测报告格式设计

## 概述

本文档定义AFW3000防火墙设备检测脚本的报告输出格式，确保报告内容简洁明了、便于阅读和存档。

## 报告文件规范

### 1. 文件命名规范

```bash
# 报告文件命名格式，防火墙软件SN码取自节点1系统信息
afw3000_check_软件SN码_YYYYMMDD_HHMMSS.txt

# 示例
afw3000_check_000007700124082924708402_20250722_143000.txt  # 2025年7月22日 14:30:00生成

# 存储位置
report/afw3000_check_000007700124082924708402_YYYYMMDD_HHMMSS.txt
```

### 2. 文件编码和格式

```bash
文件属性：
├── 编码格式：UTF-8
├── 换行符：Unix格式 (LF)
├── 文件权限：644 (rw-r--r--)
└── 最大行宽：80字符
```

## 报告内容结构

### 1. 报告模板结构

```text
========================================
AFW3000防火墙设备检测报告
========================================

检测时间：YYYY-MM-DD HH:MM:SS
检测范围：[检测范围描述]
脚本版本：[版本号]

========================================
设备检测概况
========================================

总节点数：[数量]
检测成功：[数量]
检测失败：[数量]
检测成功率：[百分比]

========================================
设备详细检测结果
========================================

[设备检测结果详情]

报告生成时间：YYYY-MM-DD HH:MM:SS
```

### 2. 详细内容格式

#### 2.1 数据显示规范

**未获取数据的显示规则：**
- 所有无法获取或不可用的数据字段统一显示为 `-` 符号
- 避免使用"未获取到"、"未知"、"N/A"等冗长描述
- 保持报告简洁直观，提高可读性

**树状结构符号规范：**
- 使用 `├──` 表示非最后一项的子项目
- 使用 `└──` 表示最后一项的子项目
- 确保缩进层级正确，便于阅读

**示例：**
```text
├── 内存：
│   ├── 总容量：8GB
│   └── 内存条列表：
│       ├── 内存条1: 型号=DDR4-2666, 容量=4GB, 类型=DDR4
│       └── 内存条2: 型号=-, 容量=4GB, 类型=DDR4
├── 存储：
│   ├── 总容量：256GB
│   └── 硬盘列表：
│       └── 硬盘1: 型号=Samsung SSD, 容量=256GB, 类型=SSD
```

#### 2.2 报告头部格式
```text
AFW3000防火墙设备检测报告
========================================

检测时间：2025-07-22 14:30:00
检测范围：所有节点 (N1, N2, N3)
脚本版本：1.0
```

#### 2.2 设备概况格式
```text
========================================
设备检测概况
========================================

总节点数：3
检测成功：2
检测失败：1
检测成功率：66.7%
```

#### 2.3 设备详细结果格式
```text
========================================
设备详细检测结果
========================================

[N1节点 - *************]
├── 网络检测：正常
│   ├── snmp端口 163：开放
│   ├── https端口 443：开放
│   └── ssh端口 36863：关闭
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：E2000Q_NT06
│   │   ├── 制造商：Phytium
│   │   └── MCU版本：V15-0.0.13
│   ├── CPU：
│   │   ├── 型号：Phytium,FT-E2000Q
│   │   ├── 核数：4核
│   │   └── 频率：core 0~1: 1500.0MHz, core 2~3: 2000.0MHz
│   ├── 内存：
│   │   ├── 总容量：4096M
│   │   └── 内存条列表：
│   │       └── 内存条1: 型号=DDR4-2400, 容量=4096M, 类型=DDR4
│   ├── 存储：
│   │   ├── 总容量：Flash: 29.50G, Disk: 469G
│   │   └── 硬盘列表：
│   │       ├── 硬盘1: 型号=eMMC Flash, 容量=29.50G, 类型=Flash
│   │       └── 硬盘2: 型号=SATA SSD, 容量=469G, 类型=SSD
│   └── 网口：
│       ├── 网口总数：6个千兆网口
│       └── 网口列表：
│           ├── ge0: IP=*************/24, MAC=00:1a:2b:3c:4d:5e, 速率=1000Mbps
│           ├── ge1: IP=*************/24, MAC=00:1a:2b:3c:4d:5f, 速率=1000Mbps
│           └── ge2-ge5: 未配置
├── 系统信息：正常
│   ├── 系统版本：
│   │   ├── 防火墙版本：V3.0 (Build Jul 8 2024 17:34:15)
│   │   ├── 固件版本：FW-V5.2-R6.0-NT-20240708-ALL.BIN
│   │   └── 平台：PLATFORM_NT06
│   ├── 系统状态：
│   │   ├── 运行时间：33天0小时20分钟
│   │   ├── CPU温度：34.0°C
│   │   ├── 主板温度：27.125°C
│   │   └── 启动方式：冷启动
│   └── 软件序列号：000007700124082924708402
└── 软件信息：正常
    ├── 防火墙软件：AFW3000 V3.0 (运行中)
    ├── 应用签名版本：20240306 (运行中)
    ├── URL分类版本：20240306 (运行中)
    ├── IPS签名版本：20240307 (运行中)
    ├── 病毒防护版本：20240306 (运行中)
    └── WAF规则版本：20240306 (运行中)

[N2节点 - *************]
├── 网络检测：正常
│   ├── 端口22：开放
│   ├── 端口80：开放
│   ├── 端口443：开放
│   └── 端口8080：开放
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：Custom ARM Board
│   │   ├── 制造商：Unknown
│   │   └── BIOS版本：U-Boot 2020.01
│   ├── CPU：
│   │   ├── 型号：ARM Cortex-A72
│   │   ├── 核数：4核
│   │   └── 频率：2000.0MHz
│   ├── 内存：
│   │   ├── 总容量：8GB
│   │   └── 内存条列表：
│   │       └── 内存条1: 型号=DDR4-2666, 容量=8GB, 类型=DDR4
│   ├── 存储：
│   │   ├── 总容量：256GB
│   │   └── 硬盘列表：
│   │       └── 硬盘1: 型号=Samsung SSD, 容量=256GB, 类型=SSD
│   └── 网口：
│       ├── 网口总数：3个接口
│       └── 网口列表：
│           ├── eth0: IP=*************/24, MAC=52:54:00:12:34:56, 速率=1000Mbps
│           └── lo: IP=127.0.0.1/8, MAC=00:00:00:00:00:00, 速率=-
├── 系统信息：异常
│   ├── 系统版本：
│   │   ├── 发行版本：UOS 20 1050
│   │   └── 内核版本：4.19.0-amd64-desktop
│   └── 系统状态：
│       ├── CPU使用率：85% [超过阈值80%]
│       ├── 内存使用率：90% [超过阈值85%]
│       ├── 硬盘使用率：75%
│       └── 系统负载：3.2 [超过阈值2.0]
└── 软件信息：正常
    ├── 防火墙软件：AFW3000 v2.0.5 (运行中)
    ├── SSH服务：OpenSSH 8.2 (运行中)
    ├── Web服务：apache 2.4.41 (运行中)
    └── 数据库服务：PostgreSQL 12.8 (运行中)

[N3节点 - *************]
├── 网络检测：异常
│   └── 连接失败：无法建立SSH连接
└── 后续检测：跳过 (网络检测失败)
```

#### 2.4 报告尾部格式
```text
报告生成时间：2025-07-22 14:35:00
```



## 报告生成函数设计

### 1. 报告生成主函数

```bash
# 生成完整检测报告
generate_report() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local report_file="report/afw3000_check_$timestamp.txt"
    local check_scope="$1"  # all/n1/n2/n3

    # 创建报告目录
    mkdir -p "report"

    # 生成报告
    {
        generate_report_header "$check_scope"
        generate_device_summary
        generate_device_details
        generate_report_footer
    } > "$report_file"

    echo "检测报告已生成: $report_file"
    return 0
}
```

### 2. 报告组件生成函数

```bash
# 生成报告头部
generate_report_header() {
    local check_scope="$1"
    local scope_desc
    
    case "$check_scope" in
        "all") scope_desc="所有节点 (N1, N2, N3)" ;;
        "n1")  scope_desc="N1节点" ;;
        "n2")  scope_desc="N2节点" ;;
        "n3")  scope_desc="N3节点" ;;
        *)     scope_desc="其他范围" ;;
    esac
    
    cat << EOF
AFW3000防火墙设备检测报告
========================================

检测时间：$(date '+%Y-%m-%d %H:%M:%S')
检测范围：$scope_desc
脚本版本：1.0

EOF
}

# 生成设备概况
generate_device_summary() {
    cat << EOF
========================================
设备检测概况
========================================

总设备数：$TOTAL_DEVICES
检测成功：$SUCCESS_DEVICES
检测失败：$FAILED_DEVICES
成功率：$SUCCESS_RATE%

EOF
}

# 生成设备详细信息
generate_device_details() {
    cat << EOF
========================================
设备详细检测结果
========================================

$DEVICE_DETAILS

EOF
}



# 生成报告尾部
generate_report_footer() {
    cat << EOF
报告生成时间：$(date '+%Y-%m-%d %H:%M:%S')
EOF
}
```

## 报告输出示例

### 1. 完整报告示例

```text
========================================
AFW3000防火墙设备检测报告
========================================

检测时间：2025-07-22 14:30:00
检测范围：所有节点 (N1, N2, N3)
脚本版本：1.0

========================================
设备检测概况
========================================

总节点数：3
检测成功：2
检测失败：1
成功率：66.7%

========================================
设备详细检测结果
========================================

[N1节点 - *************]
├── 网络检测：正常
│   ├── snmp端口 163：开放
│   ├── https端口 443：开放
│   └── ssh端口 36863：关闭
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：E2000Q_NT06
│   │   ├── 制造商：Phytium
│   │   └── MCU版本：V15-0.0.13
│   ├── CPU：
│   │   ├── 型号：Phytium,FT-E2000Q
│   │   └── 核心数：4核 (core 0~1: 1500.0MHz, core 2~3: 2000.0MHz)
│   ├── 内存：
│   │   └── 容量：4096M
│   ├── 存储：
│   │   └── 容量：Flash: 29.50G, Disk: 469G
│   └── 网口：
│       ├── 型号：6个千兆网口 (ge0-ge5)
│       └── 配置：ge0 (*************/24), ge1 (*************/24)
├── 系统信息：正常
│   ├── 系统版本：
│   │   ├── 防火墙版本：V3.0 (Build Jul 8 2024 17:34:15)
│   │   ├── 固件版本：FW-V5.2-R6.0-NT-20240708-ALL.BIN
│   │   └── 平台：PLATFORM_NT06
│   ├── 系统状态：
│   │   ├── 运行时间：33天0小时20分钟
│   │   ├── CPU温度：34.0°C
│   │   ├── 主板温度：27.125°C
│   │   └── 启动方式：冷启动
│   └── 软件序列号：000007700124082924708402
└── 软件信息：正常
    ├── 防火墙软件：AFW3000 V3.0 (运行中)
    ├── 应用签名版本：20240306 (运行中)
    ├── URL分类版本：20240306 (运行中)
    ├── IPS签名版本：20240307 (运行中)
    ├── 病毒防护版本：20240306 (运行中)
    └── WAF规则版本：20240306 (运行中)

[N2节点 - *************]
├── 网络检测：正常
│   ├── 端口22：开放
│   ├── 端口80：开放
│   ├── 端口443：开放
│   └── 端口8080：开放
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：Custom ARM Board
│   │   ├── 制造商：Unknown
│   │   └── BIOS版本：U-Boot 2020.01
│   ├── CPU：
│   │   ├── 型号：ARM Cortex-A72
│   │   └── 核心数：4核
│   ├── 内存：
│   │   └── 容量：8GB DDR4
│   ├── 存储：
│   │   └── 容量：256GB SSD
│   └── 网口：
│       ├── 型号：Realtek RTL8111 (1Gbps)
│       └── 配置：eth0 (*************/24)
├── 系统信息：异常
│   ├── 系统版本：
│   │   ├── 发行版本：UOS 20 1050
│   │   └── 内核版本：4.19.0-amd64-desktop
│   └── 系统状态：
│       ├── CPU使用率：85% [超过阈值80%]
│       ├── 内存使用率：90% [超过阈值85%]
│       ├── 硬盘使用率：75%
│       └── 系统负载：3.2 [超过阈值2.0]
└── 软件信息：正常
    ├── 防火墙软件：AFW3000 v2.0.5 (运行中)
    ├── SSH服务：OpenSSH 8.2 (运行中)
    ├── Web服务：apache 2.4.41 (运行中)
    └── 数据库服务：PostgreSQL 12.8 (运行中)

[N3节点 - *************]
├── 网络检测：异常
│   └── 连接失败：无法建立SSH连接
└── 后续检测：跳过 (网络检测失败)

报告生成时间：2025-07-22 14:35:00
```

### 2. 单设备报告示例

```text
AFW3000防火墙设备检测报告
========================================

检测时间：2025-07-22 14:30:00
检测范围：N1节点
脚本版本：1.0

========================================
设备检测概况
========================================

总设备数：1
检测成功：1
检测失败：0
成功率：100%

========================================
设备详细检测结果
========================================

[N1节点 - *************]
├── 网络检测：正常
│   ├── snmp端口 163：开放
│   ├── https端口 443：开放
│   └── ssh端口 36863：关闭
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：E2000Q_NT06
│   │   ├── 制造商：Phytium
│   │   └── MCU版本：V15-0.0.13
│   ├── CPU：
│   │   ├── 型号：Phytium,FT-E2000Q
│   │   └── 核心数：4核 (core 0~1: 1500.0MHz, core 2~3: 2000.0MHz)
│   ├── 内存：
│   │   └── 容量：4096M
│   ├── 存储：
│   │   └── 容量：Flash: 29.50G, Disk: 469G
│   └── 网口：
│       ├── 型号：6个千兆网口 (ge0-ge5)
│       └── 配置：ge0 (*************/24), ge1 (*************/24)
├── 系统信息：正常
│   ├── 系统版本：
│   │   ├── 防火墙版本：V3.0 (Build Jul 8 2024 17:34:15)
│   │   ├── 固件版本：FW-V5.2-R6.0-NT-20240708-ALL.BIN
│   │   └── 平台：PLATFORM_NT06
│   ├── 系统状态：
│   │   ├── 运行时间：33天0小时20分钟
│   │   ├── CPU温度：34.0°C
│   │   ├── 主板温度：27.125°C
│   │   └── 启动方式：冷启动
│   └── 软件序列号：000007700124082924708402
└── 软件信息：正常
    ├── 防火墙软件：AFW3000 V3.0 (运行中)
    ├── 应用签名版本：20240306 (运行中)
    ├── URL分类版本：20240306 (运行中)
    ├── IPS签名版本：20240307 (运行中)
    ├── 病毒防护版本：20240306 (运行中)
    └── WAF规则版本：20240306 (运行中)

报告生成时间：2025-07-22 14:35:00
```

---

**报告设计原则：**
- **简洁明了**：信息层次清晰，便于快速理解
- **标准格式**：统一的格式规范，便于自动化处理
- **详细完整**：包含所有必要的检测信息
- **易于存档**：文件命名包含时间戳，便于历史记录
- **可读性强**：使用树形结构和符号，提高可读性
