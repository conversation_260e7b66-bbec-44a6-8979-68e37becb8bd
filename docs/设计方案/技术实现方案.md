# AFW3000防火墙设备检测脚本技术实现建议（精简优化版）

## 概述

基于核心检测需求，本文档提供精简实用的Shell脚本实现方案，专注于网络连通性、系统信息（系统配置，系统状态）、硬件信息（硬件型号，硬件配置）、软件版本四大核心检测功能，支持N1、N2、N3三个节点的检测。

## 整体架构设计（精简版）

### 1. 架构图

```
AFW3000检测脚本架构（精简版）
├── check_afw3000.sh              (主入口脚本)
├── modules/                      (检测模块目录)
│   ├── network_check.sh          (网络连通性检测)
│   ├── system_check.sh           (系统状态检测)
│   ├── hardware_check.sh         (硬件信息检测)
│   └── software_check.sh         (软件版本检测)
├── lib/                          (公共库目录)
│   ├── common_functions.sh       (公共函数库)
│   └── ssh_utils.sh              (SSH工具函数)
├── config/                       (配置文件目录)
│   ├── devices.conf              (设备配置)
│   └── check_config.conf         (检测配置)
└── report/                       (报告输出目录)
    └── afw3000_check_YYYYMMDD_HHMMSS.txt
```

### 2. 模块化架构设计

#### 2.1 主入口模块 (check_afw3000.sh)
```
主入口脚本职责：
├── 参数解析和验证
│   ├── all - 检测所有节点（N1、N2、N3）
│   ├── n1  - 仅检测N1节点
│   ├── n2  - 仅检测N2节点
│   └── n3  - 仅检测N3节点
├── 配置文件加载
├── 检测模块调度
├── 结果汇总处理
└── 报告生成输出
```

#### 2.2 检测模块设计
```
检测模块（modules/）
├── network_check.sh     (网络检测)
│   ├── SSH连接测试
│   └── 关键端口检测
├── hardware_check.sh    (硬件信息检测)
│   ├── 主板信息获取 (型号、制造商、BIOS)
│   ├── CPU信息获取 (型号、核心数)
│   ├── 内存信息获取 (型号、容量、类型)
│   ├── 存储信息获取 (型号、硬盘容量、类型)
│   └── 网口信息获取 (型号、配置)
├── system_check.sh      (系统信息检测)
│   ├── 系统版本获取 (发行版本、内核版本)
│   └── 系统状态监控 (CPU、内存、硬盘使用率、负载)
└── software_check.sh    (软件信息检测，根据配置中的“安装路径”和“检测命令”来)
    ├── 软件1 (版本、运行状态)
    ├── 软件2 (版本、运行状态)
    └── ...
```

#### 2.3 公共库模块 (lib/)
```
公共函数库 (common_functions.sh)
├── 日志输出函数
│   ├── log_info()    - 信息日志
│   ├── log_warn()    - 警告日志
│   └── log_error()   - 错误日志
├── 配置读取函数
│   ├── load_config() - 加载配置文件
│   └── get_device_config() - 获取设备配置
├── 结果处理函数
│   ├── format_result() - 格式化检测结果
│   └── generate_report() - 生成检测报告
└── 工具函数
    ├── check_dependencies() - 检查依赖工具
    └── validate_params() - 参数验证

SSH工具函数 (ssh_utils.sh)
├── ssh_connect() - SSH连接测试
├── ssh_execute() - SSH命令执行
└── ssh_cleanup() - SSH连接清理
```

## 配置文件设计

### 1. 设备配置文件 (config/devices.conf)

```bash
# AFW3000设备配置文件（精简版）

# N1节点配置
N1_HOST="*************"
N1_USER="admin"
N1_PASSWORD="password123"

# N2节点配置
N2_HOST="*************"
N2_USER="admin"
N2_PASSWORD="password123"

# N3节点配置
N3_HOST="*************"
N3_USER="admin"
N3_PASSWORD="password123"

# Web服务认证配置（如果需要）
WEB_USERNAME="admin"
WEB_PASSWORD="webpassword"
```

### 2. 检测配置文件 (config/check_config.conf)

```bash
# AFW3000检测配置文件（精简版）

# 网络检测配置
PING_COUNT=3
PING_TIMEOUT=5
CHECK_PORTS="22,80,443,8080"

# 系统状态检测配置
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=80

# SSH连接配置
SSH_TIMEOUT=10
SSH_RETRY_COUNT=3

# 报告配置
REPORT_DIR="report"
REPORT_FORMAT="text"
```

## 主入口脚本设计

### 1. 参数处理逻辑

```bash
# check_afw3000.sh 参数处理设计

参数验证：
├── 无参数 -> 显示使用说明
├── all    -> 检测所有节点（N1、N2、N3）
├── n1     -> 仅检测N1节点
├── n2     -> 仅检测N2节点
├── n3     -> 仅检测N3节点
└── 其他   -> 显示错误信息和使用说明

使用示例：
./check_afw3000.sh all    # 检测所有节点
./check_afw3000.sh n1     # 仅检测N1节点
./check_afw3000.sh n2     # 仅检测N2节点
./check_afw3000.sh n3     # 仅检测N3节点
```

### 2. 执行流程设计

```bash
主入口执行流程：
1. 参数解析和验证
2. 加载配置文件
3. 检查依赖工具
4. 根据参数确定检测目标
5. 依次执行检测模块
6. 汇总检测结果
7. 生成文本报告
8. 输出报告路径
```

## 检测模块接口设计

### 1. 模块接口规范

```bash
# 检测模块统一接口设计

模块输入参数：
- $1: 设备名称 (n1/n2/n3)
- $2: 设备IP地址
- $3: SSH用户名
- $4: SSH密码

模块输出格式：
- 标准输出：检测结果（格式化文本）
- 返回值：0=成功，1=失败

模块命名规范：
- modules/network_check.sh
- modules/system_check.sh
- modules/hardware_check.sh
- modules/software_check.sh
```

### 2. 模块调用示例

```bash
# 主入口脚本中的模块调用示例

# 网络连通性检测
./modules/network_check.sh "$device_name" "$device_host" "$device_user" "$device_password"

# 系统状态检测
./modules/system_check.sh "$device_name" "$device_host" "$device_user" "$device_password"

# 硬件信息检测
./modules/hardware_check.sh "$device_name" "$device_host" "$device_user" "$device_password"

# 软件版本检测
./modules/software_check.sh "$device_name" "$device_host" "$device_user" "$device_password"
```

## 报告输出设计

### 1. 报告文件命名规范

```bash
报告文件命名：
- 文件名格式：afw3000_check_软件序列码（SN码）_YYYYMMDD_HHMMSS.txt
- 存储目录：report/
- 示例：report/afw3000_check_000007700124082924708402_20250722_143000.txt
```

### 2. 报告内容结构

```text
AFW3000防火墙设备检测报告
========================================

检测时间：2025-07-22 14:30:00
检测范围：所有节点 (N1, N2, N3)
脚本版本：1.0

========================================
设备检测概况
========================================

总设备数：3
检测成功：2
检测失败：1
成功率：66.7%

========================================
设备详细检测结果
========================================

[N1节点 - *************]
├── 网络检测：正常
│   ├── snmp端口 163：开放
│   ├── https端口 443：开放
│   └── ssh端口 36863：关闭
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：E2000Q_NT06
│   │   ├── 制造商：Phytium
│   │   └── MCU版本：V15-0.0.13
│   ├── CPU：
│   │   ├── 型号：Phytium,FT-E2000Q
│   │   └── 核心数：4核 (core 0~1: 1500.0MHz, core 2~3: 2000.0MHz)
│   ├── 内存：
│   │   └── 容量：4096M
│   ├── 存储：
│   │   └── 容量：Flash: 29.50G, Disk: 469G
│   └── 网口：
│       ├── 型号：6个千兆网口 (ge0-ge5)
│       └── 配置：ge0 (*************/24), ge1 (198.92.10.234/24)
├── 系统信息：正常
│   ├── 系统版本：
│   │   ├── 防火墙版本：V3.0 (Build Jul 8 2024 17:34:15)
│   │   ├── 固件版本：FW-V5.2-R6.0-NT-20240708-ALL.BIN
│   │   └── 平台：PLATFORM_NT06
│   ├── 系统状态：
│   │   ├── 运行时间：33天0小时20分钟
│   │   ├── CPU温度：34.0°C
│   │   ├── 主板温度：27.125°C
│   │   └── 启动方式：冷启动
│   └── 软件序列号：000007700124082924708402
└── 软件信息：正常
    ├── 防火墙软件：AFW3000 V3.0 (运行中)
    ├── 应用签名版本：20240306 (运行中)
    ├── URL分类版本：20240306 (运行中)
    ├── IPS签名版本：20240307 (运行中)
    ├── 病毒防护版本：20240306 (运行中)
    └── WAF规则版本：20240306 (运行中)

[N2节点 - *************]
├── 网络检测：正常
├── 硬件信息：正常，其他参考N1节点
├── 系统信息：异常
└── 软件信息：正常，其他参考N1节点

[N3节点 - *************]
├── 网络检测：异常
│   └── 连接失败：无法建立SSH连接
└── 后续检测：跳过 (网络检测失败)

报告生成时间：2025-07-22 14:35:00
```

## 技术实现要点

### 1. 依赖工具检查

```bash
必需工具列表：
├── ssh      - SSH连接
├── sshpass  - SSH密码认证
├── ping     - 网络连通性测试
├── curl     - HTTP请求测试
├── dmidecode - 硬件信息获取
└── awk/sed  - 文本处理

可选工具列表：
├── nc       - 端口检测
└── timeout  - 命令超时控制
```

### 2. 错误处理策略

```bash
错误处理原则：
├── SSH连接失败 -> 跳过该设备，记录错误
├── 单项检测失败 -> 继续其他检测，标记异常
├── 配置文件缺失 -> 退出脚本，提示错误
└── 依赖工具缺失 -> 在检测工具内部集成
```

### 3. 性能优化考虑

```bash
性能优化策略：
├── 串行检测设备（避免并发复杂性）
├── 合理设置超时时间
├── 减少不必要的SSH连接
└── 缓存SSH连接状态
```

## 实施计划

### 1. 开发阶段

```
第一阶段：基础框架搭建
├── 1.1 项目结构初始化
│   ├── 创建目录结构 (modules/, lib/, config/, report/)
│   ├── 设置文件权限和执行权限
│   └── 完成标准：目录结构完整，权限设置正确
├── 1.2 配置文件实现
│   ├── 创建 devices.conf (设备连接配置)
│   ├── 创建 check_config.conf (检测参数配置)
│   └── 完成标准：配置文件格式正确，参数完整
├── 1.3 公共函数库开发
│   ├── 实现 common_functions.sh (日志、配置读取、结果处理)
│   ├── 实现 ssh_utils.sh (SSH连接、执行、清理)
│   └── 完成标准：函数接口定义明确，基础功能可用
└── 1.4 主入口脚本框架
    ├── 实现参数解析和验证逻辑
    ├── 实现配置文件加载机制
    ├── 实现依赖工具检查功能
    └── 完成标准：脚本可正常启动，参数处理正确

第二阶段：检测模块开发（增量式开发策略）
├── 2.1 网络检测模块 (network_check.sh)
│   ├── 实现SSH连接测试功能
│   ├── 实现关键端口检测功能
│   ├── 实现检测依赖逻辑 (失败时跳过后续检测)
│   ├── 集成到主入口脚本 (afw3000_check.sh)
│   ├── 更新报告生成功能 (按照报告格式设计，添加网络检测结果部分)
│   └── 完成标准：模块独立运行正常，主入口脚本集成成功，报告显示网络检测结果，数据显示已实际检测为准，没有就使用 - 占位。
├── 2.2 硬件信息检测模块 (hardware_check.sh)
│   ├── 实现主板信息获取 (型号、制造商、MCU/BIOS版本)
│   ├── 实现CPU信息获取 (型号、配置：核数，频率)
│   ├── 实现内存信息获取 (总内存容量，每个内存条信息（型号，容量，类型），列表展示)
│   ├── 实现存储信息获取 (总容量，每个硬盘信息（型号，容量，类型），列表展示)
│   ├── 实现网口信息获取 (每个网口的型号、配置（网卡名，IP地址/子网掩码，MAC地址，速率），列表展示)
│   ├── 集成到主入口脚本 (在网络检测成功后调用)
│   ├── 更新报告生成功能 (按照报告格式设计，添加硬件信息检测结果部分)
│   └── 完成标准：模块独立运行正常，主入口脚本集成成功，报告显示硬件检测结果，数据显示已实际检测为准，没有就使用 - 占位。
├── 2.3 系统信息检测模块 (system_check.sh)
│   ├── 实现系统版本获取 (系统发行版本，内核版本，系统架构，主机名称)
│   ├── 实现系统状态监控 (系统负载，运行时间、启动方式)
│   ├── 实现软件序列号获取（只针对n1节点，在 "show ver" 命令返回中提取 Software S/N 值）
│   ├── 集成到主入口脚本 (在硬件检测完成后调用)
│   ├── 更新报告生成功能 (按照报告格式设计，添加系统信息检测结果部分)
│   └── 完成标准：模块独立运行正常，主入口脚本集成成功，报告显示系统检测结果（格式遵守 报告格式设计），数据显示已实际检测为准。
└── 2.4 软件信息检测模块 (software_check.sh)
    ├── n1节点（AFW3000设备）检测方案
    │   ├── 检测内容：Web服务的运行状态和基本信息
    │   ├── 实现参考：基于现有的 scripts/check_afw3000_web.sh 脚本的检测逻辑
    │   ├── 检测方式：通过HTTP/HTTPS连接验证Web服务可用性
    │   ├── 检测指标：Web服务状态、响应时间、登录是否正常
    │   └── 技术实现：使用curl命令进行HTTP/HTTPS连接测试
    ├── n2/n3节点（Linux系统）检测方案
    │   ├── 检测内容：三大类目软件的安装状态、运行状态、版本信息
    │   ├── 安全套件类目检测
    │   │   ├── 检测内容：安全套件是一个包含多个小组件服务的综合软件包
    │   │   ├── 实现参考：基于现有的 scripts/djt_check.sh 脚本的检测逻辑
    │   │   ├── 检测组件：watchdog（系统看门狗）、nmap（网络扫描工具）、wireguard（VPN服务）、salt（配置管理客户端）、安全代理、zabbix agent（监控代理）、socks5代理、SVN工具、Sysdig监控工具、Jenkins JDK、BEEP中继服务等
    │   │   ├── 配置化设计：检测的服务信息从配置文件中读取
    │   │   │   ├── 服务名称列表和友好显示名称
    │   │   │   ├── 服务安装路径（如：/root/secure-tools/watchdog/、/home/<USER>/等）
    │   │   │   ├── 检测命令（如：systemctl status、tail log文件、执行特定命令等）
    │   │   │   └── 版本获取命令（如：--version、--help、show命令等）
    │   │   └── 技术实现：基于systemctl、文件检查、命令执行等方式进行服务状态检测
    │   ├── 安全加固类目检测
    │   │   ├── 检测内容：安全加固工具的安装状态和配置完整性
    │   │   ├── 检测方法：
    │   │   │   ├── 检查 指定 secure_check 目录是否存在
    │   │   │   └── 检查 secure_check/log/exec_shell.log 日志文件是否不为空（即有执行记录）
    │   │   ├── 版本信息：暂时先从 secure_check.tar.gz 上获取，没有就跳过
    │   │   └── 技术实现：基于文件系统检查等方式
    │   ├── 其他软件类目检测
    │   │   ├── 检测内容：其他非安全套件和非安全加固的软件
    │   │   ├── 配置化设计：检测的服务信息从 config/software_check.conf 配置文件中读取
    │   │   ├── 检测方法：基于命令可用性检查、版本信息获取、服务状态查询等
    │   │   └── 技术实现：基于文件系统检查、命令执行等方式
    │   ├── 配置文件位置：config/software_check.conf
    │   └── 报告展示：按三个类目分别展示安装成功和安装失败的软件列表
    ├── 集成到主入口脚本 (在系统检测完成后调用)
    ├── 更新报告生成功能 (按照报告格式设计，添加软件信息检测结果部分)
    └── 完成标准：模块独立运行正常，主入口脚本集成成功，报告显示软件检测结果（格式遵守 报告格式设计），数据显示已实际检测为准，没有就使用 - 占位。

增量式开发原则：
├── 每个模块完成后立即集成到主入口脚本
├── 每个模块完成后立即更新报告生成功能
├── 未完成的模块在报告中显示为"待开发"状态
├── 已完成的模块在报告中显示实际检测结果
└── 确保每个阶段都有可观测的完整输出结果

第三阶段：系统优化与完善
├── 3.1 报告格式优化
│   ├── 优化报告输出格式 (结构化、美观性)
│   ├── 实现报告模板系统 (支持不同输出格式)
│   ├── 增强报告内容完整性检查
│   ├── 实现报告数据统计和汇总功能
│   └── 完成标准：报告格式专业，内容完整，易于阅读
├── 3.2 错误处理完善
│   ├── 完善SSH连接失败处理机制
│   ├── 实现检测超时处理机制
│   ├── 实现异常情况恢复机制
│   ├── 增强模块间错误传播机制
│   └── 完成标准：错误处理健全，系统稳定性高
├── 3.3 功能测试验证
│   ├── 单模块功能测试 (每个检测模块独立测试)
│   ├── 端到端流程测试 (完整检测流程验证)
│   ├── 异常场景测试 (网络断开、权限不足等)
│   ├── 增量集成测试 (验证每个模块集成后的整体功能)
│   └── 完成标准：所有测试用例通过，功能稳定可靠
└── 3.4 性能优化调整
    ├── 优化SSH连接复用机制
    ├── 优化检测并发执行逻辑
    ├── 优化报告生成效率
    ├── 实现检测结果缓存机制
    └── 完成标准：检测效率提升，资源占用合理

注意：由于采用增量式开发策略，原第三阶段的"模块集成开发"任务已分散到第二阶段各模块中，
第三阶段重点转向系统级优化、测试验证和完善工作。
```

## 增量式开发策略详细说明

### 1. 开发流程

```
增量式开发流程：
├── 模块开发阶段
│   ├── 1. 开发单个检测模块 (如 network_check.sh)
│   ├── 2. 模块独立功能测试
│   ├── 3. 集成到主入口脚本
│   ├── 4. 更新报告生成功能
│   └── 5. 端到端测试验证
├── 持续集成验证
│   ├── 每个模块完成后立即可观测效果
│   ├── 报告中显示已完成模块的实际结果
│   ├── 报告中显示未完成模块的"待开发"状态
│   └── 确保每个阶段都有完整可用的系统
└── 迭代优化
    ├── 基于实际运行效果调整模块设计
    ├── 持续优化报告格式和内容
    └── 逐步完善错误处理和性能
```

### 2. 报告生成策略

```
报告内容动态生成策略：
├── 已完成模块
│   ├── 显示实际检测结果
│   ├── 包含详细的检测数据
│   └── 提供成功/失败状态
├── 未完成模块
│   ├── 显示"模块开发中"状态
│   ├── 预留报告结构框架
│   └── 说明预期功能描述
└── 报告完整性
    ├── 始终生成完整结构的报告
    ├── 动态填充已完成部分的内容
    └── 保持报告格式的一致性
```

### 3. 集成验证标准

```
每个模块的集成验证标准：
├── 模块独立运行测试
│   ├── 参数验证正确
│   ├── 功能执行正常
│   ├── 错误处理得当
│   └── 输出格式规范
├── 主入口脚本集成测试
│   ├── 模块调用成功
│   ├── 参数传递正确
│   ├── 返回值处理正确
│   └── 错误传播正常
└── 报告生成验证
    ├── 报告包含模块结果
    ├── 格式符合规范
    ├── 内容准确完整
    └── 未完成模块显示合理
```

### 4. 部署验证

```
部署验证步骤：
1. 环境依赖检查
2. 配置文件配置
3. SSH连接测试
4. 单模块功能测试
5. 完整流程测试
6. 报告输出验证
```

---

**设计原则总结：**
- **简洁实用**：专注核心功能，避免过度设计
- **模块化**：清晰的模块分离，便于维护
- **统一入口**：参数化执行，使用简单
- **配置分离**：认证信息统一管理
- **标准输出**：文本格式报告，便于阅读和存档
