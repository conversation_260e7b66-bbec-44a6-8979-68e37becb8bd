# sshpass跨平台编译指导

本文档提供了为不同架构和发行版编译sshpass工具的详细步骤，以确保AFW3000检测项目的跨平台兼容性。

## 目录结构

项目支持以下目录结构来存放预编译的sshpass二进制文件：

```
tools/
├── x86_64/
│   ├── centos/sshpass          # CentOS/RHEL x86_64版本
│   ├── ubuntu/sshpass          # Ubuntu x86_64版本
│   └── sshpass                 # 通用x86_64版本
├── aarch64/
│   ├── kylin/sshpass           # 银河麒麟 aarch64版本
│   ├── uos/sshpass             # UOS aarch64版本
│   └── sshpass                 # 通用aarch64版本
├── sshpass-x86_64              # 架构特定命名
├── sshpass-aarch64             # 架构特定命名
└── sshpass                     # 通用版本（兼容性最低）
```

## 查找优先级

系统会按以下优先级自动查找sshpass：

1. `tools/{架构}/{发行版}/sshpass` (最高优先级)
2. `tools/{架构}/sshpass`
3. `tools/sshpass-{架构}`
4. `tools/{发行版}/sshpass`
5. `tools/sshpass-{发行版}`
6. `tools/sshpass` (通用版本)
7. 系统级sshpass (最低优先级)

## 常见平台编译指导

### 1. x86_64 CentOS/RHEL 7/8

```bash
# 安装编译依赖
sudo yum install -y gcc make wget

# 下载源码
cd /tmp
wget https://sourceforge.net/projects/sshpass/files/sshpass/1.09/sshpass-1.09.tar.gz/download -O sshpass-1.09.tar.gz

# 编译
tar -xzf sshpass-1.09.tar.gz
cd sshpass-1.09
./configure --prefix=/usr/local
make

# 复制到项目目录
mkdir -p /path/to/afw3000-check/tools/x86_64/centos
cp sshpass /path/to/afw3000-check/tools/x86_64/centos/sshpass
chmod +x /path/to/afw3000-check/tools/x86_64/centos/sshpass
```

### 2. aarch64 银河麒麟

```bash
# 安装编译依赖
sudo apt update
sudo apt install -y gcc make wget

# 下载源码
cd /tmp
wget https://sourceforge.net/projects/sshpass/files/sshpass/1.09/sshpass-1.09.tar.gz/download -O sshpass-1.09.tar.gz

# 编译
tar -xzf sshpass-1.09.tar.gz
cd sshpass-1.09
./configure --prefix=/usr/local
make

# 复制到项目目录
mkdir -p /path/to/afw3000-check/tools/aarch64/kylin
cp sshpass /path/to/afw3000-check/tools/aarch64/kylin/sshpass
chmod +x /path/to/afw3000-check/tools/aarch64/kylin/sshpass
```

### 3. aarch64 UOS 20

```bash
# 安装编译依赖
sudo apt update
sudo apt install -y gcc make wget

# 下载源码
cd /tmp
wget https://sourceforge.net/projects/sshpass/files/sshpass/1.09/sshpass-1.09.tar.gz/download -O sshpass-1.09.tar.gz

# 编译
tar -xzf sshpass-1.09.tar.gz
cd sshpass-1.09
./configure --prefix=/usr/local
make

# 复制到项目目录
mkdir -p /path/to/afw3000-check/tools/aarch64/uos
cp sshpass /path/to/afw3000-check/tools/aarch64/uos/sshpass
chmod +x /path/to/afw3000-check/tools/aarch64/uos/sshpass
```

### 4. x86_64 Ubuntu/Debian

```bash
# 安装编译依赖
sudo apt update
sudo apt install -y gcc make wget

# 下载源码
cd /tmp
wget https://sourceforge.net/projects/sshpass/files/sshpass/1.09/sshpass-1.09.tar.gz/download -O sshpass-1.09.tar.gz

# 编译
tar -xzf sshpass-1.09.tar.gz
cd sshpass-1.09
./configure --prefix=/usr/local
make

# 复制到项目目录
mkdir -p /path/to/afw3000-check/tools/x86_64/ubuntu
cp sshpass /path/to/afw3000-check/tools/x86_64/ubuntu/sshpass
chmod +x /path/to/afw3000-check/tools/x86_64/ubuntu/sshpass
```

## 交叉编译（高级）

如果需要在一个平台上为另一个平台编译sshpass：

### 在x86_64上为aarch64编译

```bash
# 安装交叉编译工具链
sudo yum install -y gcc-aarch64-linux-gnu

# 编译
cd /tmp/sshpass-1.09
./configure --host=aarch64-linux-gnu --prefix=/usr/local
make CC=aarch64-linux-gnu-gcc

# 验证架构
file sshpass
# 应该显示: ELF 64-bit LSB executable, ARM aarch64
```

## 验证编译结果

编译完成后，验证二进制文件：

```bash
# 检查文件信息
file /path/to/sshpass
ls -la /path/to/sshpass

# 检查动态库依赖
ldd /path/to/sshpass

# 测试运行
/path/to/sshpass -V
```

## 自动化脚本

可以使用项目提供的构建脚本：

```bash
# 在目标环境中运行
cd /path/to/afw3000-check
chmod +x tools/build_sshpass.sh
./tools/build_sshpass.sh
```

## 故障排除

### 1. 编译错误

**问题**: `configure: error: C compiler cannot create executables`
**解决**: 安装gcc编译器
```bash
# CentOS/RHEL
sudo yum install -y gcc

# Ubuntu/Debian
sudo apt install -y gcc
```

**问题**: `make: command not found`
**解决**: 安装make工具
```bash
# CentOS/RHEL
sudo yum install -y make

# Ubuntu/Debian
sudo apt install -y make
```

### 2. 运行时错误

**问题**: `./sshpass: cannot execute binary file: Exec format error`
**解决**: 架构不匹配，需要为目标架构重新编译

**问题**: `./sshpass: error while loading shared libraries`
**解决**: 缺少动态库依赖，检查ldd输出并安装相应库

### 3. 网络问题

**问题**: 无法下载源码
**解决**: 
1. 配置代理: `export http_proxy=http://proxy:port`
2. 手动下载源码包
3. 使用镜像站点

## 发行版ID对照表

| 发行版 | ID值 | 架构 | 推荐路径 |
|--------|------|------|----------|
| CentOS 7/8 | centos | x86_64 | tools/x86_64/centos/sshpass |
| RHEL 7/8 | rhel | x86_64 | tools/x86_64/rhel/sshpass |
| Ubuntu | ubuntu | x86_64/aarch64 | tools/{arch}/ubuntu/sshpass |
| 银河麒麟 | kylin | aarch64 | tools/aarch64/kylin/sshpass |
| UOS 20 | uos | aarch64 | tools/aarch64/uos/sshpass |
| Debian | debian | x86_64/aarch64 | tools/{arch}/debian/sshpass |

## 检查当前系统信息

使用以下命令检查当前系统的架构和发行版信息：

```bash
# 检查架构
uname -m

# 检查发行版
cat /etc/os-release | grep "^ID="

# 或者使用项目的检测函数
cd /path/to/afw3000-check
source lib/ssh_utils.sh
detect_system_info
```

## 联系支持

如果在编译过程中遇到问题，请提供以下信息：

1. 目标系统架构: `uname -m`
2. 发行版信息: `cat /etc/os-release`
3. 编译错误日志
4. gcc版本: `gcc --version`

这将有助于快速定位和解决问题。
