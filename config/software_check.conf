# 软件信息检测配置文件
# 用于配置Linux系统（n2/n3节点）的服务检测
#
# 配置格式说明：
# [服务名称]
# name = 显示名称
# type = 检测类型 (systemctl|command|file|custom)
# check_cmd = 检测命令
# version_cmd = 版本获取命令（可选）
# install_path = 安装路径（可选）
# description = 服务描述

# ============================================================================
# 安全套件类目检测配置
# ============================================================================

[watchdog]
name = Watchdog服务
type = command
category = security_suite
check_cmd = tail /root/secure-tools/watchdog/feed-dog.log >/dev/null 2>&1 || tail /root/watchdog/feed-dog.log >/dev/null 2>&1
version_cmd = -
install_path = /root/secure-tools/watchdog/
description = 系统看门狗服务

[nmap]
name = Nmap工具
type = command
category = security_suite
check_cmd = type nmap >/dev/null 2>&1
version_cmd = nmap --version 2>/dev/null | head -1
install_path = -
description = 网络扫描工具

[wireguard]
name = WireGuard VPN
type = custom
category = security_suite
check_cmd = check_wireguard_service
version_cmd = -
install_path = -
description = WireGuard VPN服务

[salt]
name = Salt Minion
type = systemctl
category = security_suite
check_cmd = systemctl status ydsomn-minion >/dev/null 2>&1
version_cmd = salt-minion --version 2>/dev/null | head -1
install_path = -
description = Salt配置管理客户端

[security_agent]
name = 安全代理
type = command
category = security_suite
check_cmd = /home/<USER>/bin/admin.sh show >/dev/null 2>&1
version_cmd = /home/<USER>/bin/admin.sh version 2>/dev/null
install_path = /home/<USER>/
description = Linux安全代理

[zabbix_agent]
name = Zabbix Agent
type = systemctl
category = security_suite
check_cmd = systemctl status ydMonitorAgent >/dev/null 2>&1
version_cmd = zabbix_agentd --version 2>/dev/null | head -1
install_path = -
description = Zabbix监控代理

[socks5_proxy]
name = SOCKS5代理
type = systemctl
category = security_suite
check_cmd = systemctl status 3proxy.service >/dev/null 2>&1
version_cmd = -
install_path = -
description = SOCKS5代理服务

[svn]
name = SVN工具
type = command
category = security_suite
check_cmd = which svn >/dev/null 2>&1
version_cmd = svn --version 2>/dev/null | head -1
install_path = -
description = Subversion版本控制工具

[sysdig]
name = Sysdig工具
type = command
category = security_suite
check_cmd = sysdig --version >/dev/null 2>&1
version_cmd = sysdig --version 2>/dev/null | head -1
install_path = -
description = 系统监控和故障排查工具

[jenkins_jdk]
name = Jenkins JDK
type = custom
category = security_suite
check_cmd = check_jenkins_jdk
version_cmd = java --version 2>/dev/null | head -1
install_path = -
description = Jenkins构建工具JDK

# ============================================================================
# 安全加固类目检测配置
# ============================================================================

[secure_hardening]
name = 安全加固工具
type = custom
category = security_hardening
check_cmd = check_secure_hardening
version_cmd = -
install_path = /opt/secure_check
description = 安全加固自动化工具

# ============================================================================
# 其他软件类目检测配置
# ============================================================================

[beep_relay]
name = BEEP中继服务
type = command
category = other_software
check_cmd = /usr/local/socrelay/bin/beeprelay -? >/dev/null 2>&1
version_cmd = /usr/local/socrelay/bin/beeprelay -v 2>/dev/null
install_path = /usr/local/socrelay/
description = BEEP协议中继服务

# ============================================================================
# 全局配置参数
# ============================================================================

[global]
# 单个服务检测超时时间（秒）
service_check_timeout = 10

# 版本获取超时时间（秒）
version_check_timeout = 5

# 成功状态显示文本
success_text = 运行中

# 失败状态显示文本
failure_text = 未安装

# 版本信息获取失败时的占位符
version_placeholder = -

# 是否启用详细日志
verbose_logging = true

# 是否显示检测命令
show_commands = false

# ============================================================================
# 自定义检测函数配置
# ============================================================================

[wireguard_config]
# WireGuard服务需要检查多个组件
# 检测逻辑：scpserver服务 + wg工具 + 配置文件
components = scpserver,wg,/etc/wireguard/wg_sm.conf

[jenkins_jdk_config]
# Jenkins JDK需要检查特定版本
expected_version = 21.0.4
