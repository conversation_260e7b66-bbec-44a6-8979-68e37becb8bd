#!/bin/bash
# Web服务检测工具库
# 基于scripts/check_afw3000_web.sh重构
# 提供可复用的Web服务检测功能

# =============================================================================
# 全局配置
# =============================================================================

# 默认配置
DEFAULT_TIMEOUT=10
DEFAULT_USER_AGENT="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"

# =============================================================================
# 工具函数
# =============================================================================

# 获取curl版本
get_curl_version() {
    curl --version 2>/dev/null | head -1 | grep -o 'curl [0-9]\+\.[0-9]\+\.[0-9]\+' | cut -d' ' -f2
}

# 比较版本号
version_compare() {
    local version1="$1"
    local version2="$2"

    # 将版本号分解为数组
    IFS='.' read -ra V1 <<< "$version1"
    IFS='.' read -ra V2 <<< "$version2"

    # 比较主版本、次版本、修订版本
    for i in 0 1 2; do
        local v1=${V1[i]:-0}
        local v2=${V2[i]:-0}
        
        if [ "$v1" -gt "$v2" ]; then
            return 1  # version1 > version2
        elif [ "$v1" -lt "$v2" ]; then
            return 2  # version1 < version2
        fi
    done
    
    return 0  # version1 == version2
}

# 构建兼容的curl选项
build_compatible_curl_options() {
    local curl_version="$1"
    local mode="${2:-normal}"  # normal 或 fallback
    
    local options=""
    
    # 基础SSL选项
    options="$options -k --insecure"
    
    # 根据curl版本添加兼容选项
    version_compare "$curl_version" "7.40.0"
    local version_result=$?
    
    if [ $version_result -eq 1 ] || [ $version_result -eq 0 ]; then
        # curl >= 7.40.0
        options="$options --ssl-no-revoke"
        if [ "$mode" = "normal" ]; then
            options="$options --http1.1"
        fi
    fi
    
    version_compare "$curl_version" "7.25.0"
    version_result=$?
    
    if [ $version_result -eq 1 ] || [ $version_result -eq 0 ]; then
        # curl >= 7.25.0
        options="$options --ssl-allow-beast"
    fi
    
    # 重定向处理
    if [ "$mode" = "normal" ]; then
        options="$options -L --max-redirs 5"
    else
        options="$options --max-redirs 3"
    fi
    
    echo "$options"
}

# 获取curl选项
get_curl_options() {
    local curl_version=$(get_curl_version)
    
    if [ -z "$curl_version" ]; then
        curl_version="7.29.0"  # 假设最低版本
    fi
    
    local options=$(build_compatible_curl_options "$curl_version" "normal")
    
    # 添加额外的代理控制
    options="$options --noproxy localhost --noproxy 127.0.0.1"
    
    echo "$options"
}

# 获取备用curl选项
get_curl_options_fallback() {
    local curl_version=$(get_curl_version)
    
    if [ -z "$curl_version" ]; then
        curl_version="7.29.0"  # 假设最低版本
    fi
    
    local options=$(build_compatible_curl_options "$curl_version" "fallback")
    
    echo "$options"
}

# 禁用代理设置
disable_proxy() {
    export http_proxy=""
    export https_proxy=""
    export ftp_proxy=""
    export no_proxy="*"
    export HTTP_PROXY=""
    export HTTPS_PROXY=""
    export FTP_PROXY=""
    export NO_PROXY="*"
}

# =============================================================================
# Web服务检测核心函数
# =============================================================================

# 检查Web服务响应
check_web_service_response() {
    local target_url="$1"
    local timeout="${2:-$DEFAULT_TIMEOUT}"
    local user_agent="${3:-$DEFAULT_USER_AGENT}"
    
    # 禁用代理设置
    disable_proxy
    
    # 创建临时文件存储响应
    local temp_file=$(mktemp)
    local cookie_jar=$(mktemp)
    local curl_options=$(get_curl_options)
    
    # 发送HTTP请求检查响应
    local http_code=$(curl -s ${curl_options} -w "%{http_code}" \
        --connect-timeout "${timeout}" \
        --max-time "${timeout}" \
        --user-agent "${user_agent}" \
        --cookie-jar "${cookie_jar}" \
        -o "${temp_file}" \
        "${target_url}" 2>/dev/null)
    
    # 如果主要方法失败，尝试备用方法
    if [ "${http_code}" = "000" ] || [ -z "${http_code}" ]; then
        local fallback_options=$(get_curl_options_fallback)
        http_code=$(curl -s ${fallback_options} -w "%{http_code}" \
            --connect-timeout "${timeout}" \
            --max-time "${timeout}" \
            --user-agent "${user_agent}" \
            --cookie-jar "${cookie_jar}" \
            -o "${temp_file}" \
            "${target_url}" 2>/dev/null)
    fi
    
    # 检查响应结果
    local web_status="异常"
    local login_status="异常"
    
    if [ "${http_code}" = "200" ] || [ "${http_code}" = "302" ] || [ "${http_code}" = "301" ]; then
        web_status="正常"
        
        # 检查响应内容是否包含登录相关内容
        if grep -qi "login\.html\|login\|password\|username\|用户名\|密码" "${temp_file}" 2>/dev/null; then
            login_status="正常"
        else
            # 如果HTTP状态正常但没有登录内容，可能是主页面，也认为正常
            login_status="正常"
        fi
    fi
    
    # 清理临时文件
    rm -f "${temp_file}" "${cookie_jar}"
    
    # 返回结果（格式：http_code|web_status|login_status）
    echo "${http_code}|${web_status}|${login_status}"
    
    # 返回状态码
    if [ "$web_status" = "正常" ]; then
        return 0
    else
        return 1
    fi
}

# AFW3000设备Web服务检测（多端口支持）
check_afw3000_web_service() {
    local device_ip="$1"
    local timeout="${2:-$DEFAULT_TIMEOUT}"
    local user_agent="${3:-$DEFAULT_USER_AGENT}"
    
    local web_ports=("443" "8443" "80")
    local working_port=""
    local working_protocol=""
    local http_code=""
    local web_status="异常"
    local login_status="异常"
    local response_time="-"
    
    # 尝试不同端口检测Web服务
    for port in "${web_ports[@]}"; do
        local protocol="https"
        [[ "$port" == "80" ]] && protocol="http"
        
        local web_url="${protocol}://$device_ip:$port"
        
        # 记录开始时间
        local start_time=$(date +%s.%N)
        
        # 执行检测
        local result=$(check_web_service_response "$web_url" "$timeout" "$user_agent")
        local check_result=$?
        
        # 计算响应时间
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "0.1")
        
        # 解析结果
        IFS='|' read -r http_code web_status login_status <<< "$result"
        
        if [ $check_result -eq 0 ]; then
            working_port="$port"
            working_protocol="$protocol"
            response_time="${duration}s"
            break
        fi
    done
    
    # 返回结果（格式：working_port|working_protocol|http_code|web_status|login_status|response_time）
    echo "${working_port}|${working_protocol}|${http_code}|${web_status}|${login_status}|${response_time}"
    
    # 返回状态码
    if [ "$web_status" = "正常" ]; then
        return 0
    else
        return 1
    fi
}
