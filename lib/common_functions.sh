#!/bin/bash
# 描述：公共函数库

# =============================================================================
# 日志输出函数
# =============================================================================

# 信息日志输出
# 参数: $1 - 日志消息
log_info() {
    local message="$1"
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $message"
}

# 警告日志输出
# 参数: $1 - 警告消息
log_warn() {
    local message="$1"
    echo "[WARN] $(date '+%Y-%m-%d %H:%M:%S') $message" >&2
}

# 错误日志输出
# 参数: $1 - 错误消息
log_error() {
    local message="$1"
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $message" >&2
}

# =============================================================================
# 配置读取函数
# =============================================================================

# 加载配置文件
# 参数: $1 - 配置文件路径
# 返回: 0=成功, 1=失败
load_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 加载配置文件
    source "$config_file"
    log_info "配置文件加载成功: $config_file"
    return 0
}

# 获取设备配置
# 参数: $1 - 设备名称 (n1/n2/n3)
# 输出: HOST USER PASSWORD PORT [BACKUP_USER BACKUP_PASSWORD]
get_device_config() {
    local device_name="$1"
    local device_upper=$(echo "$device_name" | tr '[:lower:]' '[:upper:]')
    
    # 获取基础配置
    local host_var="${device_upper}_HOST"
    local user_var="${device_upper}_USER"
    local password_var="${device_upper}_PASSWORD"
    local port_var="${device_upper}_PORT"
    
    local host="${!host_var}"
    local user="${!user_var}"
    local password="${!password_var}"
    local port="${!port_var}"
    
    # 检查必需配置
    if [[ -z "$host" || -z "$user" || -z "$password" ]]; then
        log_error "设备 $device_name 配置不完整"
        return 1
    fi
    
    # 获取备用用户配置（如果存在）
    local backup_user_var="${device_upper}_BACKUP_USER"
    local backup_password_var="${device_upper}_BACKUP_PASSWORD"
    local backup_user="${!backup_user_var}"
    local backup_password="${!backup_password_var}"
    
    # 输出配置信息
    echo "$host $user $password ${port:-22} $backup_user $backup_password"
    return 0
}

# =============================================================================
# 结果处理函数
# =============================================================================

# 格式化检测结果
# 参数: $1 - 设备名称, $2 - 检测类型, $3 - 检测结果, $4 - 详细信息
format_result() {
    local device_name="$1"
    local check_type="$2"
    local result="$3"
    local details="$4"
    
    echo "[$device_name] $check_type: $result"
    if [[ -n "$details" ]]; then
        echo "$details" | sed 's/^/  /'
    fi
}

# 生成检测报告
# 参数: $1 - 报告文件路径, $2 - 检测结果数据
generate_report() {
    local report_file="$1"
    local report_data="$2"
    
    # 创建报告目录
    local report_dir=$(dirname "$report_file")
    mkdir -p "$report_dir"
    
    # 生成报告头部
    {
        echo "AFW3000防火墙设备检测报告"
        echo "========================================"
        echo ""
        echo "检测时间：$(date '+%Y-%m-%d %H:%M:%S')"
        echo "脚本版本：1.0"
        echo ""
        echo "========================================"
        echo "设备详细检测结果"
        echo "========================================"
        echo ""
        echo "$report_data"
        echo ""
        echo "报告生成时间：$(date '+%Y-%m-%d %H:%M:%S')"
    } > "$report_file"
    
    log_info "检测报告已生成: $report_file"
    return 0
}

# =============================================================================
# 工具函数
# =============================================================================

# 检查依赖工具
# 返回: 0=所有工具可用, 1=有工具缺失
check_dependencies() {
    local required_tools=("ssh" "ping" "curl" "awk" "sed")
    local missing_tools=()

    # 检查基础工具
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done

    # 检查sshpass (使用ssh_utils.sh中的智能检测)
    local sshpass_path
    if sshpass_path=$(get_sshpass_path 2>/dev/null); then
        log_info "sshpass可用: $sshpass_path"

        # 检测sshpass类型和架构兼容性
        local system_info=$(detect_system_info 2>/dev/null || echo "unknown:unknown")
        local arch=$(echo "$system_info" | cut -d':' -f1)
        local distro=$(echo "$system_info" | cut -d':' -f2)

        if [[ "$sshpass_path" == *"sshpass-$arch"* ]]; then
            log_info "使用架构特定sshpass ($arch)"
        elif [[ "$sshpass_path" == *"tools/sshpass"* ]]; then
            log_info "使用项目内通用sshpass"
        else
            log_info "使用系统级sshpass"
        fi
    else
        log_warn "sshpass不可用，将尝试自动安装"
        missing_tools+=("sshpass")
    fi

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必需工具: ${missing_tools[*]}"

        # 如果只是缺少sshpass，提供解决建议
        if [[ ${#missing_tools[@]} -eq 1 && "${missing_tools[0]}" == "sshpass" ]]; then
            local system_info=$(detect_system_info 2>/dev/null || echo "unknown:unknown")
            local arch=$(echo "$system_info" | cut -d':' -f1)
            local distro=$(echo "$system_info" | cut -d':' -f2)

            log_info "sshpass解决方案:"
            log_info "1. 手动编译sshpass并放置到: tools/$arch/$distro/sshpass"
            log_info "2. 安装系统级sshpass: yum/apt/dnf install sshpass"
            log_info "3. 参考编译指导: docs/sshpass_compilation_guide.md"
            log_info "4. 当前系统: $arch ($distro)"
        fi

        return 1
    fi

    log_info "依赖工具检查通过"
    return 0
}

# 参数验证
# 参数: $1 - 参数值, $2 - 参数名称, $3 - 验证类型 (required/ip/port)
validate_params() {
    local param_value="$1"
    local param_name="$2"
    local validation_type="$3"
    
    case "$validation_type" in
        "required")
            if [[ -z "$param_value" ]]; then
                log_error "参数 $param_name 不能为空"
                return 1
            fi
            ;;
        "ip")
            if [[ ! "$param_value" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
                log_error "参数 $param_name 不是有效的IP地址: $param_value"
                return 1
            fi
            ;;
        "port")
            if [[ ! "$param_value" =~ ^[0-9]+$ ]] || [[ "$param_value" -lt 1 ]] || [[ "$param_value" -gt 65535 ]]; then
                log_error "参数 $param_name 不是有效的端口号: $param_value"
                return 1
            fi
            ;;
    esac
    
    return 0
}
