#!/bin/bash
# SSH工具函数库
# 包含SSH连接、命令执行、连接清理等功能

# 引入公共函数库
source "$(dirname "${BASH_SOURCE[0]}")/common_functions.sh"

# =============================================================================
# sshpass工具路径检测
# =============================================================================

# 检测系统架构和发行版信息
# 返回: "架构:发行版" 格式的字符串
detect_system_info() {
    local arch=$(uname -m)
    local distro="unknown"

    # 检测发行版
    if [[ -f /etc/os-release ]]; then
        distro=$(grep '^ID=' /etc/os-release | cut -d'=' -f2 | tr -d '"')
    elif [[ -f /etc/redhat-release ]]; then
        if grep -q "CentOS" /etc/redhat-release; then
            distro="centos"
        elif grep -q "Red Hat" /etc/redhat-release; then
            distro="rhel"
        fi
    fi

    echo "${arch}:${distro}"
}

# 获取适合当前系统的sshpass路径
# 支持多架构和跨平台自动适配
# 返回: sshpass工具的完整路径
get_sshpass_path() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    local system_info=$(detect_system_info)
    local arch=$(echo "$system_info" | cut -d':' -f1)
    local distro=$(echo "$system_info" | cut -d':' -f2)

    # 按优先级顺序查找预编译sshpass二进制文件
    local search_paths=(
        # 1. 架构特定版本
        "$script_dir/tools/$arch/sshpass"
        # 2. 架构+发行版特定版本
        "$script_dir/tools/$arch/$distro/sshpass"
        "$script_dir/tools/sshpass-$arch"
        # 3. 发行版特定版本
        "$script_dir/tools/$distro/sshpass"
        "$script_dir/tools/sshpass-$distro"
    )

    # 遍历搜索路径
    for sshpass_path in "${search_paths[@]}"; do
        if [[ -x "$sshpass_path" ]]; then
            # 验证可执行性
            if timeout 3 "$sshpass_path" -V >/dev/null 2>&1; then
                echo "$sshpass_path"
                return 0
            fi
        fi
    done

    # 尝试系统级sshpass
    if command -v sshpass >/dev/null 2>&1; then
        echo "sshpass"
        return 0
    fi

    # 没有找到可用的sshpass，输出详细错误信息
    log_error "未找到适合当前系统的sshpass工具"
    log_error "当前系统信息: $arch ($distro)"
    log_error "已搜索的路径:"
    for path in "${search_paths[@]}"; do
        log_error "  $path"
    done
    log_error ""
    log_error "解决方案:"
    log_error "1. 手动编译sshpass并放置到以下任一路径:"
    log_error "   - $script_dir/tools/$arch/sshpass"
    log_error "   - $script_dir/tools/$arch/$distro/sshpass"
    log_error "2. 安装系统级sshpass: yum/apt/dnf install sshpass"

    return 1
}



# =============================================================================
# SSH连接函数
# =============================================================================

# SSH连接测试
# 参数: $1 - 主机地址, $2 - 用户名, $3 - 密码, $4 - 端口号
# 返回: 0=连接成功, 1=连接失败
ssh_connect() {
    local host="$1"
    local user="$2"
    local password="$3"
    local port="${4:-22}"
    
    # 参数验证
    validate_params "$host" "host" "required" || return 1
    validate_params "$user" "user" "required" || return 1
    validate_params "$password" "password" "required" || return 1
    validate_params "$port" "port" "port" || return 1
    
    log_info "测试SSH连接: $user@$host:$port"

    # 获取sshpass工具路径
    local sshpass_cmd
    sshpass_cmd=$(get_sshpass_path)
    if [[ $? -ne 0 ]]; then
        log_error "sshpass工具不可用"
        return 1
    fi

    # 使用sshpass进行SSH连接测试
    # 根据设备类型使用不同的测试命令
    local test_commands=("echo 'SSH连接测试成功'" "show ver")
    local connection_success=false

    for cmd in "${test_commands[@]}"; do
        if "$sshpass_cmd" -p "$password" ssh -o ConnectTimeout=10 \
                                              -o StrictHostKeyChecking=no \
                                              -o UserKnownHostsFile=/dev/null \
                                              -o LogLevel=quiet \
                                              -p "$port" \
                                              "$user@$host" \
                                              "$cmd" >/dev/null 2>&1; then
            log_info "SSH连接成功: $user@$host:$port (使用命令: $cmd)"
            connection_success=true
            break
        fi
    done

    if [[ "$connection_success" == true ]]; then
        return 0
    else
        log_error "SSH连接失败: $user@$host:$port"
        return 1
    fi
}

# SSH连接测试（支持备用用户）
# 参数: $1 - 主机地址, $2 - 主用户名, $3 - 主密码, $4 - 端口号, $5 - 备用用户名, $6 - 备用密码
# 输出: 成功的用户名
# 返回: 0=连接成功, 1=连接失败
ssh_connect_with_backup() {
    local host="$1"
    local user="$2"
    local password="$3"
    local port="${4:-22}"
    local backup_user="$5"
    local backup_password="$6"
    
    # 首先尝试主用户连接
    if ssh_connect "$host" "$user" "$password" "$port"; then
        echo "$user"
        return 0
    fi
    
    # 如果主用户失败且有备用用户，尝试备用用户连接
    if [[ -n "$backup_user" && -n "$backup_password" ]]; then
        log_warn "主用户连接失败，尝试备用用户: $backup_user@$host:$port"
        if ssh_connect "$host" "$backup_user" "$backup_password" "$port"; then
            echo "$backup_user"
            return 0
        fi
    fi
    
    log_error "所有用户连接均失败: $host:$port"
    return 1
}

# =============================================================================
# SSH命令执行函数
# =============================================================================

# SSH命令执行
# 参数: $1 - 主机地址, $2 - 用户名, $3 - 密码, $4 - 端口号, $5 - 要执行的命令
# 输出: 命令执行结果
# 返回: 0=执行成功, 1=执行失败
ssh_execute() {
    local host="$1"
    local user="$2"
    local password="$3"
    local port="${4:-22}"
    local command="$5"
    
    # 参数验证
    validate_params "$host" "host" "required" || return 1
    validate_params "$user" "user" "required" || return 1
    validate_params "$password" "password" "required" || return 1
    validate_params "$port" "port" "port" || return 1
    validate_params "$command" "command" "required" || return 1
    
    log_info "执行SSH命令: $user@$host:$port - $command"

    # 获取sshpass工具路径
    local sshpass_cmd
    sshpass_cmd=$(get_sshpass_path)
    if [[ $? -ne 0 ]]; then
        log_error "sshpass工具不可用"
        return 1
    fi

    # 执行SSH命令
    local result
    result=$("$sshpass_cmd" -p "$password" ssh -o ConnectTimeout=10 \
                                               -o StrictHostKeyChecking=no \
                                               -o UserKnownHostsFile=/dev/null \
                                               -o LogLevel=quiet \
                                               -p "$port" \
                                               "$user@$host" \
                                               "$command" 2>&1)
    local exit_code=$?

    if [[ $exit_code -eq 0 ]]; then
        log_info "SSH命令执行成功"
        echo "$result"
        return 0
    else
        log_error "SSH命令执行失败 (退出码: $exit_code)"
        echo "$result" >&2
        return 1
    fi
}

# SSH命令执行（支持备用用户）
# 参数: $1 - 主机地址, $2 - 主用户名, $3 - 主密码, $4 - 端口号, $5 - 命令, $6 - 备用用户名, $7 - 备用密码
# 输出: 命令执行结果
# 返回: 0=执行成功, 1=执行失败
ssh_execute_with_backup() {
    local host="$1"
    local user="$2"
    local password="$3"
    local port="${4:-22}"
    local command="$5"
    local backup_user="$6"
    local backup_password="$7"
    
    # 首先尝试主用户执行
    if ssh_execute "$host" "$user" "$password" "$port" "$command"; then
        return 0
    fi
    
    # 如果主用户失败且有备用用户，尝试备用用户执行
    if [[ -n "$backup_user" && -n "$backup_password" ]]; then
        log_warn "主用户执行失败，尝试备用用户: $backup_user@$host:$port"
        if ssh_execute "$host" "$backup_user" "$backup_password" "$port" "$command"; then
            return 0
        fi
    fi
    
    log_error "所有用户执行均失败: $host:$port"
    return 1
}

# =============================================================================
# SSH连接清理函数
# =============================================================================

# SSH连接清理
# 功能: 清理SSH相关的临时文件和连接
ssh_cleanup() {
    log_info "清理SSH连接和临时文件"
    
    # 清理SSH known_hosts临时条目（如果有的话）
    # 这里主要是日志记录，实际清理逻辑可以根据需要扩展
    
    log_info "SSH连接清理完成"
    return 0
}

# 批量SSH连接清理
# 参数: $@ - 主机地址列表
ssh_cleanup_hosts() {
    local hosts=("$@")

    for host in "${hosts[@]}"; do
        log_info "清理主机连接: $host"
        # 这里可以添加针对特定主机的清理逻辑
    done

    ssh_cleanup
    return 0
}


