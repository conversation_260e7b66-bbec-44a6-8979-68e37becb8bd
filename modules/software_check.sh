#!/bin/bash
# 软件信息检测模块
# 功能: n1设备Web服务检测、n2/n3设备指定服务检测
# 版本: 1.0

# =============================================================================
# 模块初始化
# =============================================================================

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$SCRIPT_DIR"

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    log_error "无法找到SSH工具库 lib/ssh_utils.sh"
    exit 1
fi

if [[ -f "lib/web_check_utils.sh" ]]; then
    source "lib/web_check_utils.sh"
else
    log_error "无法找到Web检测工具库 lib/web_check_utils.sh"
    exit 1
fi

# =============================================================================
# 模块信息
# =============================================================================

MODULE_NAME="软件信息检测模块"
MODULE_VERSION="1.0"

# =============================================================================
# 配置文件加载
# =============================================================================

# 加载软件检测配置
load_software_config() {
    local config_file="config/software_check.conf"

    if [[ ! -f "$config_file" ]]; then
        log_error "软件检测配置文件不存在: $config_file"
        return 1
    fi

    # 读取新格式的配置文件
    SOFTWARE_SERVICES=()
    local current_service=""
    local service_name=""
    local service_type=""
    local service_category=""
    local check_cmd=""
    local version_cmd=""
    local install_path=""
    local description=""

    while IFS= read -r line; do
        # 跳过注释行和空行
        [[ "$line" =~ ^[[:space:]]*# ]] && continue
        [[ -z "$line" ]] && continue
        [[ "$line" =~ ^[[:space:]]*$ ]] && continue

        # 检测服务段开始
        if [[ "$line" =~ ^\[([^]]+)\]$ ]]; then
            # 保存上一个服务配置
            if [[ -n "$current_service" && "$current_service" != "global" && "$current_service" != "wireguard_config" && "$current_service" != "jenkins_jdk_config" ]]; then
                SOFTWARE_SERVICES+=("$current_service|$service_name|$service_type|$service_category|$check_cmd|$version_cmd|$install_path|$description")
            fi

            # 开始新服务
            current_service="${BASH_REMATCH[1]}"
            service_name=""
            service_type=""
            service_category=""
            check_cmd=""
            version_cmd=""
            install_path=""
            description=""
            continue
        fi

        # 解析键值对
        if [[ "$line" =~ ^[[:space:]]*([^=]+)[[:space:]]*=[[:space:]]*(.*)$ ]]; then
            local key="${BASH_REMATCH[1]// /}"
            local value="${BASH_REMATCH[2]}"

            case "$key" in
                "name") service_name="$value" ;;
                "type") service_type="$value" ;;
                "category") service_category="$value" ;;
                "check_cmd") check_cmd="$value" ;;
                "version_cmd") version_cmd="$value" ;;
                "install_path") install_path="$value" ;;
                "description") description="$value" ;;
            esac
        fi
    done < "$config_file"

    # 保存最后一个服务配置
    if [[ -n "$current_service" && "$current_service" != "global" && "$current_service" != "wireguard_config" && "$current_service" != "jenkins_jdk_config" ]]; then
        SOFTWARE_SERVICES+=("$current_service|$service_name|$service_type|$service_category|$check_cmd|$version_cmd|$install_path|$description")
    fi

    log_info "加载了 ${#SOFTWARE_SERVICES[@]} 个服务配置"
    return 0
}

# =============================================================================
# 参数验证和解析
# =============================================================================

validate_parameters() {
    if [[ $# -lt 4 || $# -gt 5 ]]; then
        log_error "参数错误"
        echo "用法: $0 <设备名称> <设备IP> <SSH用户名> <SSH密码> [SSH端口]"
        echo "示例: $0 n1 ************* admin admin@123 36863"
        return 1
    fi
    
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    
    # 验证设备名称
    if [[ ! "$device_name" =~ ^(n1|n2|n3)$ ]]; then
        log_error "无效的设备名称: $device_name (支持: n1, n2, n3)"
        return 1
    fi
    
    # 验证IP地址格式
    if [[ ! "$device_ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        log_error "无效的IP地址格式: $device_ip"
        return 1
    fi
    
    # 验证用户名和密码非空
    if [[ -z "$ssh_user" ]]; then
        log_error "SSH用户名不能为空"
        return 1
    fi
    
    if [[ -z "$ssh_password" ]]; then
        log_error "SSH密码不能为空"
        return 1
    fi
    
    return 0
}

# =============================================================================
# n1设备Web服务检测功能
# =============================================================================

# n1设备Web服务检测（使用Web检测库）
check_n1_web_service() {
    local device_ip="$1"
    local ssh_user="$2"
    local ssh_password="$3"
    local ssh_port="${4:-22}"

    echo "=========================================="
    echo "n1设备Web服务检测"
    echo "=========================================="
    echo "设备: n1 ($device_ip)"
    echo ""

    log_info "开始n1设备Web服务检测..."

    # 使用Web检测库进行检测
    local timeout=10
    local user_agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"

    # 调用Web检测库函数
    local result=$(check_afw3000_web_service "$device_ip" "$timeout" "$user_agent")
    local check_result=$?

    # 解析结果（格式：working_port|working_protocol|http_code|web_status|login_status|response_time）
    IFS='|' read -r working_port working_protocol http_code web_status login_status response_time <<< "$result"

    # 显示检测结果
    echo "Web服务检测结果："
    if [[ $check_result -eq 0 && -n "$working_port" ]]; then
        echo "  Web服务状态: $web_status"
        echo "  访问地址: ${working_protocol}://$device_ip:$working_port"
        echo "  响应时间: $response_time"
        echo "  登录页面: $login_status"

        log_info "n1设备Web服务检测成功"
        return 0
    else
        echo "  Web服务状态: ${web_status:-"无法连接"}"
        echo "  响应时间: ${response_time:-"-"}"
        echo "  登录页面: ${login_status:-"无法访问"}"

        log_error "n1设备Web服务检测失败"
        return 1
    fi
}

# =============================================================================
# n2/n3设备服务检测功能
# =============================================================================

# 检查WireGuard服务（自定义检测）
check_wireguard_service() {
    local device_ip="$1"
    local ssh_user="$2"
    local ssh_password="$3"
    local ssh_port="$4"

    # 检查scpserver服务
    local scpserver_status=0
    if ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "systemctl status scpserver >/dev/null 2>&1" >/dev/null 2>&1; then
        scpserver_status=1
    fi

    # 检查wg工具
    local wg_tool_status=0
    if ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "wg --help >/dev/null 2>&1" >/dev/null 2>&1; then
        wg_tool_status=1
    fi

    # 检查配置文件
    local wg_config_status=0
    if ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "[ -f /etc/wireguard/wg_sm.conf ]" >/dev/null 2>&1; then
        wg_config_status=1
    fi

    # 调试输出
    log_info "WireGuard检测: scpserver=$scpserver_status, wg_tool=$wg_tool_status, config=$wg_config_status"

    if [[ $scpserver_status -eq 1 && $wg_tool_status -eq 1 && $wg_config_status -eq 1 ]]; then
        return 0
    else
        return 1
    fi
}

# 检查Jenkins JDK（自定义检测）
check_jenkins_jdk() {
    local device_ip="$1"
    local ssh_user="$2"
    local ssh_password="$3"
    local ssh_port="$4"

    # 使用与参考脚本相同的检测逻辑
    local java_version_cmd="java --version 2>/dev/null | head -n 1 | awk '{print \$2}'"
    local java_version=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "$java_version_cmd" 2>/dev/null)
    local ssh_exit_code=$?

    log_info "Jenkins JDK检测: ssh_exit_code=$ssh_exit_code, java_version='$java_version'"

    # 如果SSH命令执行失败，说明java命令不存在
    if [[ $ssh_exit_code -ne 0 ]]; then
        return 1
    fi

    # 检查java版本是否为期望值
    if [[ "$java_version" == "21.0.4" ]]; then
        return 0
    else
        return 1
    fi
}

# 检查安全加固工具（自定义检测）
check_secure_hardening() {
    local device_ip="$1"
    local ssh_user="$2"
    local ssh_password="$3"
    local ssh_port="$4"

    # 检查secure_check目录是否存在
    local secure_check_paths=("/opt/secure_check" "/root/secure_check" "/home/<USER>")
    local found_path=""

    for path in "${secure_check_paths[@]}"; do
        if ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "[ -d $path ]" >/dev/null 2>&1; then
            found_path="$path"
            break
        fi
    done

    if [[ -z "$found_path" ]]; then
        log_info "安全加固检测: secure_check目录不存在"
        return 1
    fi

    # 检查 secure_check/log/exec_shell.log 日志文件是否不为空（即有执行记录）
    local log_file="$found_path/log/exec_shell.log"
    local log_check_cmd="[ -f $log_file ] && [ -s $log_file ] && echo 'has_records' || echo 'no_records'"
    local log_status=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "$log_check_cmd" 2>/dev/null)

    log_info "安全加固检测: path=$found_path, log_status=$log_status"

    if [[ "$log_status" == "has_records" ]]; then
        return 0
    else
        return 1
    fi
}

# n2/n3设备服务检测
check_linux_services() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"
    
    echo "=========================================="
    echo "Linux系统服务检测"
    echo "=========================================="
    echo "设备: $device_name ($device_ip:$ssh_port)"
    echo ""
    
    log_info "开始Linux系统服务检测..."
    
    # 加载配置文件
    if ! load_software_config; then
        log_error "无法加载软件检测配置"
        return 1
    fi
    
    local success_count=0
    local total_count=${#SOFTWARE_SERVICES[@]}
    
    echo "检测到 $total_count 个服务配置"
    echo ""
    
    # 遍历检测每个服务
    for service_config in "${SOFTWARE_SERVICES[@]}"; do
        IFS='|' read -r service_name service_display_name check_type service_category check_cmd version_cmd install_path description <<< "$service_config"

        echo "检测服务: $service_name ($service_display_name|$service_category|$description)"
        
        local service_status="未安装"
        local service_version="-"
        
        # 根据检测类型执行不同的检测逻辑
        case "$check_type" in
            "systemctl"|"command")
                # 直接执行SSH命令并检查退出码
                ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "$check_cmd" >/dev/null 2>&1
                local exit_code=$?
                if [[ $exit_code -eq 0 ]]; then
                    service_status="运行中"
                    success_count=$((success_count + 1))
                else
                    service_status="未安装"
                fi
                ;;
            "custom")
                if [[ "$service_name" == "wireguard" ]]; then
                    if check_wireguard_service "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
                        service_status="运行中"
                        success_count=$((success_count + 1))
                    else
                        service_status="未安装"
                    fi
                elif [[ "$service_name" == "jenkins_jdk" ]]; then
                    if check_jenkins_jdk "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
                        service_status="运行中"
                        success_count=$((success_count + 1))
                    else
                        service_status="未安装"
                    fi
                elif [[ "$service_name" == "secure_hardening" ]]; then
                    if check_secure_hardening "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
                        service_status="运行中"
                        success_count=$((success_count + 1))
                    else
                        service_status="未安装"
                    fi
                fi
                ;;
        esac
        
        # 获取版本信息（如果配置了版本命令）
        if [[ "$version_cmd" != "-" && "$service_status" == "运行中" ]]; then
            service_version=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "$version_cmd" 2>/dev/null | head -1 | xargs)
            [[ -z "$service_version" ]] && service_version="-"
        fi
        
        echo "  状态: $service_status"
        echo "  版本: $service_version"
        echo ""
    done
    
    echo "=========================================="
    echo "Linux系统服务检测完成"
    echo "=========================================="
    echo "检测服务: $total_count"
    echo "运行中: $success_count"
    echo "未安装: $((total_count - success_count))"
    
    if [[ $success_count -gt 0 ]]; then
        log_info "Linux系统服务检测完成，$success_count/$total_count 个服务运行正常"
        return 0
    else
        log_warn "Linux系统服务检测完成，所有服务均未正常运行"
        return 1
    fi
}

# =============================================================================
# 软件检测主流程
# =============================================================================

run_software_check() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo "=========================================="
    echo "$MODULE_NAME $MODULE_VERSION"
    echo "=========================================="
    echo "检测时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "目标设备: $device_name"
    echo "设备地址: $device_ip:$ssh_port"
    echo ""

    local overall_success=true

    if [[ "$device_name" == "n1" ]]; then
        # n1设备：Web服务检测
        if check_n1_web_service "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
            log_info "n1设备Web服务检测成功"
        else
            overall_success=false
            log_error "n1设备Web服务检测失败"
        fi
    else
        # n2/n3设备：Linux系统服务检测
        if check_linux_services "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
            log_info "Linux系统服务检测成功"
        else
            overall_success=false
            log_error "Linux系统服务检测失败"
        fi
    fi

    echo ""
    echo "=========================================="
    echo "软件检测完成"
    echo "=========================================="

    if [[ "$overall_success" == true ]]; then
        echo "检测结果: 软件信息获取完整"
        log_info "软件检测全部通过"
        return 0
    else
        echo "检测结果: 软件信息获取不完整"
        log_warn "软件检测存在问题"
        return 1
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 验证参数
    if ! validate_parameters "$@"; then
        exit 1
    fi

    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    # 执行软件检测
    if run_software_check "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        exit 0
    else
        exit 1
    fi
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 设置错误处理
set -e
trap 'log_error "软件检测模块异常退出"; exit 1' ERR

# 调用主函数
main "$@"
