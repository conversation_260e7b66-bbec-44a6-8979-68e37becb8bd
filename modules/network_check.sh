#!/bin/bash
# 网络检测模块
# 功能: SSH连接测试和关键端口检测
# 版本: 1.0

# =============================================================================
# 模块初始化
# =============================================================================

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$SCRIPT_DIR"

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    log_error "无法找到SSH工具库 lib/ssh_utils.sh"
    exit 1
fi

# =============================================================================
# 模块信息
# =============================================================================

MODULE_NAME="网络检测模块"
MODULE_VERSION="1.0"

# =============================================================================
# 参数验证和解析
# =============================================================================

validate_parameters() {
    if [[ $# -ne 4 ]]; then
        log_error "参数错误"
        echo "用法: $0 <设备名称> <设备IP> <SSH用户名> <SSH密码>"
        echo "示例: $0 n1 ************* admin admin@123"
        return 1
    fi
    
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    
    # 验证设备名称
    if [[ ! "$device_name" =~ ^(n1|n2|n3)$ ]]; then
        log_error "无效的设备名称: $device_name (支持: n1, n2, n3)"
        return 1
    fi
    
    # 验证IP地址格式
    if [[ ! "$device_ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        log_error "无效的IP地址格式: $device_ip"
        return 1
    fi
    
    # 验证用户名和密码非空
    if [[ -z "$ssh_user" ]]; then
        log_error "SSH用户名不能为空"
        return 1
    fi
    
    if [[ -z "$ssh_password" ]]; then
        log_error "SSH密码不能为空"
        return 1
    fi
    
    return 0
}

# =============================================================================
# 网络检测功能
# =============================================================================

# SSH连接测试
test_ssh_connection() {
    
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"
    
    echo "=========================================="
    echo "SSH连接测试"
    echo "=========================================="
    echo "设备: $device_name ($device_ip:$ssh_port)"
    echo "用户: $ssh_user"
    echo ""
    
    log_info "开始SSH连接测试..."
    
    # 执行SSH连接测试
    if ssh_connect "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        echo "✓ SSH连接: 成功"
        log_info "SSH连接测试成功"
        return 0
    else
        echo "✗ SSH连接: 失败"
        log_error "SSH连接测试失败"
        return 1
    fi
}

# 关键端口检测
test_key_ports() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"
    
    echo ""
    echo "=========================================="
    echo "关键端口检测"
    echo "=========================================="
    echo "目标设备: $device_name ($device_ip)"
    echo ""
    
    # 加载检测配置
    if ! load_config "config/check_config.conf"; then
        log_error "无法加载检测配置文件"
        return 1
    fi
    
    # 获取要检测的端口列表
    local ports_to_check="${CHECK_PORTS:-22,80,443,8080}"
    log_info "检测端口列表: $ports_to_check"
    
    # 将端口字符串转换为数组
    IFS=',' read -ra PORT_ARRAY <<< "$ports_to_check"
    
    local success_count=0
    local total_count=0
    
    echo "端口检测结果:"
    
    # 逐个检测端口
    for port in "${PORT_ARRAY[@]}"; do
        # 去除空格
        port=$(echo "$port" | tr -d ' ')
        total_count=$((total_count + 1))
        
        log_info "检测端口 $port..."
        
        # 使用timeout和bash内置的网络连接测试
        if timeout 5 bash -c "echo >/dev/tcp/$device_ip/$port" 2>/dev/null; then
            echo "  端口 $port: ✓ 开放"
            success_count=$((success_count + 1))
        else
            echo "  端口 $port: ✗ 关闭或不可达"
        fi
    done
    
    echo ""
    echo "端口检测统计:"
    echo "  总端口数: $total_count"
    echo "  开放端口数: $success_count"
    echo "  关闭端口数: $((total_count - success_count))"
    
    # 如果SSH端口(22)不可达，返回失败
    if [[ "$ports_to_check" == *"22"* ]]; then
        if ! timeout 5 bash -c "echo >/dev/tcp/$device_ip/22" 2>/dev/null; then
            log_warn "SSH端口(22)不可达，可能影响后续检测"
        fi
    fi
    
    return 0
}

# 网络延迟测试
test_network_latency() {
    local device_name="$1"
    local device_ip="$2"
    
    echo ""
    echo "=========================================="
    echo "网络延迟测试"
    echo "=========================================="
    echo "目标设备: $device_name ($device_ip)"
    echo ""
    
    log_info "开始网络延迟测试..."
    
    # 加载配置
    local ping_count="${PING_COUNT:-3}"
    local ping_timeout="${PING_TIMEOUT:-5}"
    
    echo "Ping测试结果:"
    
    # 执行ping测试
    local ping_result
    ping_result=$(ping -c "$ping_count" -W "$ping_timeout" "$device_ip" 2>&1)
    local ping_exit_code=$?
    
    if [[ $ping_exit_code -eq 0 ]]; then
        echo "✓ 网络连通性: 正常"
        
        # 提取延迟统计信息
        local avg_latency
        avg_latency=$(echo "$ping_result" | grep "rtt min/avg/max/mdev" | awk -F'/' '{print $5}' | awk '{print $1}')
        
        if [[ -n "$avg_latency" ]]; then
            echo "  平均延迟: ${avg_latency}ms"
        fi
        
        # 提取丢包率
        local packet_loss
        packet_loss=$(echo "$ping_result" | grep "packet loss" | awk '{for(i=1;i<=NF;i++) if($i ~ /%/) print $i}')

        if [[ -n "$packet_loss" ]]; then
            echo "  丢包率: $packet_loss"
        fi
        
        log_info "网络延迟测试成功"
        return 0
    else
        echo "✗ 网络连通性: 失败"
        echo "  错误信息: $(echo "$ping_result" | tail -1)"
        log_error "网络延迟测试失败"
        return 1
    fi
}

# =============================================================================
# 主检测流程
# =============================================================================

run_network_check() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    
    # 从配置中获取SSH端口
    local ssh_port="22"
    if [[ -f "config/devices.conf" ]]; then
        source "config/devices.conf"
        local port_var="${device_name^^}_PORT"
        ssh_port="${!port_var:-22}"
    fi
    
    echo "=========================================="
    echo "$MODULE_NAME v$MODULE_VERSION"
    echo "=========================================="
    echo "检测时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "目标设备: $device_name"
    echo "设备地址: $device_ip:$ssh_port"
    echo ""
    
    local overall_success=true
    
    # 1. 网络延迟测试（基础连通性）
    local ping_success=true
    if ! test_network_latency "$device_name" "$device_ip"; then
        log_warn "网络延迟测试失败，但继续尝试SSH连接测试"
        ping_success=false
    fi
    
    # 2. 关键端口检测
    if ! test_key_ports "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        log_warn "关键端口检测出现问题"
        overall_success=false
    fi
    
    # 3. SSH连接测试（关键测试）
    local ssh_success=true
    if ! test_ssh_connection "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        log_error "SSH连接测试失败，后续检测可能受影响"
        ssh_success=false
        overall_success=false
    fi

    echo ""
    echo "=========================================="
    echo "网络检测完成"
    echo "=========================================="

    # 只要SSH连接成功，就认为网络检测通过（即使ping失败）
    if [[ "$ssh_success" == true ]]; then
        if [[ "$ping_success" == false ]]; then
            echo "检测结果: ⚠ 网络状态部分正常（SSH可用，但ping不通）"
            log_warn "网络检测部分通过：SSH连接正常，但ping测试失败"
        else
            echo "检测结果: ✓ 网络状态正常"
            log_info "网络检测全部通过"
        fi
        return 0
    else
        echo "检测结果: ⚠ 网络状态异常"
        log_warn "网络检测失败：SSH连接不可用"
        return 1
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 验证参数
    if ! validate_parameters "$@"; then
        exit 1
    fi
    
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    
    # 执行网络检测
    if run_network_check "$device_name" "$device_ip" "$ssh_user" "$ssh_password"; then
        exit 0
    else
        exit 1
    fi
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 设置错误处理
set -e
trap 'log_error "网络检测模块异常退出"; exit 1' ERR

# 调用主函数
main "$@"
