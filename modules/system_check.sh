#!/bin/bash
# 系统信息检测模块
# 功能: 系统版本、系统状态、软件序列号检测
# 版本: 1.0

# =============================================================================
# 模块初始化
# =============================================================================

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$SCRIPT_DIR"

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    log_error "无法找到SSH工具库 lib/ssh_utils.sh"
    exit 1
fi

# =============================================================================
# 模块信息
# =============================================================================

MODULE_NAME="系统信息检测模块"
MODULE_VERSION="1.0"

# =============================================================================
# 参数验证和解析
# =============================================================================

validate_parameters() {
    if [[ $# -lt 4 || $# -gt 5 ]]; then
        log_error "参数错误"
        echo "用法: $0 <设备名称> <设备IP> <SSH用户名> <SSH密码> [SSH端口]"
        echo "示例: $0 n1 ************* admin admin@123 36863"
        return 1
    fi
    
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    
    # 验证设备名称
    if [[ ! "$device_name" =~ ^(n1|n2|n3)$ ]]; then
        log_error "无效的设备名称: $device_name (支持: n1, n2, n3)"
        return 1
    fi
    
    # 验证IP地址格式
    if [[ ! "$device_ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        log_error "无效的IP地址格式: $device_ip"
        return 1
    fi
    
    # 验证用户名和密码非空
    if [[ -z "$ssh_user" ]]; then
        log_error "SSH用户名不能为空"
        return 1
    fi
    
    if [[ -z "$ssh_password" ]]; then
        log_error "SSH密码不能为空"
        return 1
    fi
    
    return 0
}

# =============================================================================
# 系统检测功能
# =============================================================================

# 系统版本信息检测
get_system_version() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"
    
    echo "=========================================="
    echo "系统版本信息检测"
    echo "=========================================="
    echo "设备: $device_name ($device_ip:$ssh_port)"
    echo ""
    
    log_info "开始系统版本信息检测..."
    
    local version_info=""
    local hostname_info=""

    # 获取主机名称（所有设备类型通用）
    hostname_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "hostname" 2>/dev/null)

    if [[ "$device_name" == "n1" ]]; then
        # AFW3000防火墙设备使用show ver命令
        version_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
    else
        # Linux系统使用标准命令
        version_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "cat /etc/os-release && uname -a" 2>/dev/null)
    fi
    
    if [[ $? -eq 0 && -n "$version_info" ]]; then
        echo "系统版本信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # AFW3000设备版本信息解析
            local version_data=$(echo "$version_info" | grep -v "INFO\|ERROR\|WARN")
            local hostname_data=$(echo "$hostname_info" | grep -v "INFO\|ERROR\|WARN" | xargs)

            # 提取防火墙版本
            local fw_version=$(echo "$version_data" | grep -E "V[0-9]+\.[0-9]+" | head -1 | sed 's/.*\(V[0-9]\+\.[0-9]\+[^,]*\).*/\1/')

            # 提取固件版本
            local firmware_version=$(echo "$version_data" | grep "Firmware is" | cut -d' ' -f3)

            # 提取平台信息
            local platform=$(echo "$version_data" | grep "Platform" | cut -d':' -f2 | xargs)

            echo "  主机名称: ${hostname_data:-"-"}"
            echo "  防火墙版本: ${fw_version:-"-"}"
            echo "  固件版本: ${firmware_version:-"-"}"
            echo "  平台: ${platform:-"-"}"
        else
            # Linux系统版本信息解析
            local version_data=$(echo "$version_info" | grep -v "INFO\|ERROR\|WARN")
            local hostname_data=$(echo "$hostname_info" | grep -v "INFO\|ERROR\|WARN" | xargs)

            # 提取发行版本
            local distro_name=$(echo "$version_data" | grep "PRETTY_NAME" | cut -d'=' -f2 | tr -d '"')

            # 提取内核版本
            local kernel_version=$(echo "$version_data" | grep "^Linux" | awk '{print $3}' | head -1)

            # 提取系统架构
            local architecture=$(echo "$version_data" | grep "^Linux" | awk '{print $(NF-1)}' | head -1)

            echo "  主机名称: ${hostname_data:-"-"}"
            echo "  发行版本: ${distro_name:-"-"}"
            echo "  内核版本: ${kernel_version:-"-"}"
            echo "  系统架构: ${architecture:-"-"}"
        fi
        log_info "系统版本信息检测成功"
        return 0
    else
        echo "系统版本信息获取失败"
        log_error "系统版本信息检测失败"
        return 1
    fi
}

# 系统状态监控
get_system_status() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"
    
    echo ""
    echo "=========================================="
    echo "系统状态监控"
    echo "=========================================="
    echo "目标设备: $device_name ($device_ip)"
    echo ""
    
    log_info "开始系统状态监控..."
    
    local status_info=""
    if [[ "$device_name" == "n1" ]]; then
        # AFW3000防火墙设备使用show ver命令获取运行时间等信息
        status_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
    else
        # Linux系统使用标准命令
        status_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "uptime && cat /proc/loadavg && free -m | head -2 && df -h / | tail -1" 2>/dev/null)
    fi
    
    if [[ $? -eq 0 && -n "$status_info" ]]; then
        echo "系统状态信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # AFW3000设备状态信息解析 - 统一指标格式与Linux设备保持一致
            local status_data=$(echo "$status_info" | grep -v "INFO\|ERROR\|WARN")

            # 提取运行时间
            local uptime=$(echo "$status_data" | grep "System uptime" | cut -d':' -f2 | xargs)

            # AFW3000设备无法获取以下指标，使用"-"占位
            echo "  系统负载: -"
            echo "  内存使用率: -"
            echo "  硬盘使用率: -"
            echo "  运行时间: ${uptime:-"-"}"
        else
            # Linux系统状态信息解析
            local status_data=$(echo "$status_info" | grep -v "INFO\|ERROR\|WARN")
            
            # 提取系统负载
            local load_avg=$(echo "$status_data" | grep "load average" | sed 's/.*load average: //')
            
            # 提取内存使用率
            local mem_line=$(echo "$status_data" | grep "Mem:" | head -1)
            local mem_total=$(echo "$mem_line" | awk '{print $2}')
            local mem_used=$(echo "$mem_line" | awk '{print $3}')
            local mem_usage=""
            if [[ -n "$mem_total" && "$mem_total" -gt 0 ]]; then
                mem_usage=$(( (mem_used * 100) / mem_total ))
            fi
            
            # 提取硬盘使用率
            local disk_usage=$(echo "$status_data" | tail -1 | awk '{print $5}' | tr -d '%')
            
            echo "  系统负载: ${load_avg:-"-"}"
            echo "  内存使用率: ${mem_usage:-"-"}%"
            echo "  硬盘使用率: ${disk_usage:-"-"}%"
            echo "  运行时间: $(echo "$status_data" | grep "up" | sed 's/.*up //' | sed 's/,.*load.*//')"
        fi
        log_info "系统状态监控成功"
        return 0
    else
        echo "系统状态信息获取失败"
        log_error "系统状态监控失败"
        return 1
    fi
}

# 软件序列号获取（仅针对n1设备）
get_software_serial() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"
    
    echo ""
    echo "=========================================="
    echo "软件序列号获取"
    echo "=========================================="
    echo "目标设备: $device_name ($device_ip)"
    echo ""
    
    log_info "开始软件序列号获取..."
    
    if [[ "$device_name" != "n1" ]]; then
        echo "软件序列号获取跳过（仅适用于n1设备）"
        log_info "软件序列号获取跳过（仅适用于n1设备）"
        return 0
    fi
    
    # 针对n1设备，从show ver命令中提取Software S/N
    local serial_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
    
    if [[ $? -eq 0 && -n "$serial_info" ]]; then
        local serial_data=$(echo "$serial_info" | grep -v "INFO\|ERROR\|WARN")
        
        # 提取Software S/N
        local software_sn=$(echo "$serial_data" | grep "Software S/N" | cut -d':' -f2 | xargs)
        
        if [[ -n "$software_sn" ]]; then
            echo "软件序列号获取成功"
            echo "  Software S/N: $software_sn"
            log_info "软件序列号获取成功: $software_sn"
            return 0
        else
            echo "软件序列号获取失败：未找到Software S/N字段"
            log_warn "软件序列号获取失败：未找到Software S/N字段"
            return 1
        fi
    else
        echo "软件序列号获取失败"
        log_error "软件序列号获取失败"
        return 1
    fi
}

# =============================================================================
# 系统检测主流程
# =============================================================================

run_system_check() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo "=========================================="
    echo "$MODULE_NAME $MODULE_VERSION"
    echo "=========================================="
    echo "检测时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "目标设备: $device_name"
    echo "设备地址: $device_ip:$ssh_port"
    echo ""

    local total_count=3
    local success_count=0
    local overall_success=true

    # 1. 系统版本信息检测
    if get_system_version "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 2. 系统状态监控
    if get_system_status "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 3. 软件序列号获取（仅针对n1设备）
    if get_software_serial "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        # 对于非n1设备，软件序列号获取失败不影响整体结果
        if [[ "$device_name" == "n1" ]]; then
            overall_success=false
        else
            success_count=$((success_count + 1))
        fi
    fi

    echo ""
    echo "=========================================="
    echo "系统检测完成"
    echo "=========================================="
    echo "检测项目: $total_count"
    echo "成功项目: $success_count"
    echo "失败项目: $((total_count - success_count))"

    if [[ "$overall_success" == true ]]; then
        echo "检测结果: 系统信息获取完整"
        log_info "系统检测全部通过"
        return 0
    else
        echo "检测结果: ⚠ 系统信息获取不完整"
        log_warn "系统检测存在问题"
        return 1
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 验证参数
    if ! validate_parameters "$@"; then
        exit 1
    fi

    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    # 执行系统检测
    if run_system_check "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        exit 0
    else
        exit 1
    fi
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 设置错误处理
set -e
trap 'log_error "系统检测模块异常退出"; exit 1' ERR

# 调用主函数
main "$@"
