#!/bin/bash
# description：检查AFW3000防火墙设备的Web服务状态
# author：dkyzheng

# ==================== 配置参数 ====================
# 防火墙设备配置
FIREWALL_HOST="*************"
FIREWALL_PORT="443"
FIREWALL_URL="https://${FIREWALL_HOST}:${FIREWALL_PORT}"

# 登录凭据
USERNAME="admin"
PASSWORD="admin@123"

# 网络超时设置（秒）
TIMEOUT=10
PING_COUNT=3
PING_TIMEOUT=5

# HTTP请求设置
USER_AGENT="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
SKIP_SSL_VERIFY="true"

# AFW3000专用配置
LOGIN_PATHS="/login.html /login /admin /auth /signin"
SUCCESS_KEYWORDS="webui|main|dashboard|index|welcome|成功|欢迎|管理|控制台|g=main"
FAILURE_KEYWORDS="error|failed|invalid|错误|失败|用户名|密码|不正确"

# 全局状态变量
overall_status=0

# ==================== curl兼容性处理 ====================
# 获取curl版本
get_curl_version() {
    curl --version 2>/dev/null | head -1 | grep -o 'curl [0-9]\+\.[0-9]\+\.[0-9]\+' | cut -d' ' -f2
}

# 比较版本号
# 返回值: 0=相等, 1=version1>version2, 2=version1<version2
version_compare() {
    
    local version1="$1"
    local version2="$2"

    # 将版本号分解为数组
    IFS='.' read -ra V1 <<< "$version1"
    IFS='.' read -ra V2 <<< "$version2"

    # 比较主版本、次版本、修订版本
    for i in 0 1 2; do
        local v1=${V1[i]:-0}
        local v2=${V2[i]:-0}
        if [ "$v1" -gt "$v2" ]; then
            return 1  # version1 > version2
        elif [ "$v1" -lt "$v2" ]; then
            return 2  # version1 < version2
        fi
    done
    return 0  # version1 == version2
}

# 检查curl是否支持特定选项
curl_supports_option() {
    local option="$1"
    curl --help 2>/dev/null | grep -q -- "$option"
}

# 根据curl版本构建兼容的选项
build_compatible_curl_options() {

    local curl_version="$1"
    local mode="${2:-normal}"  # normal 或 fallback
    local options=""

    # 基础SSL选项（所有版本支持）
    if [ "${SKIP_SSL_VERIFY}" = "true" ]; then
        if [ "$mode" = "fallback" ]; then
            options="$options --insecure"
        else
            options="$options -k"
        fi
    fi

    # 基础选项（所有版本支持）
    options="$options -L --ipv4"

    # 代理控制（基础版本）
    options="$options --noproxy '*'"

    # 版本特定选项
    version_compare "$curl_version" "7.33.0"
    local version_result=$?
    if [ $version_result -le 1 ]; then  # >= 7.33.0
        if [ "$mode" = "fallback" ]; then
            options="$options --http1.1"
        fi
    fi

    version_compare "$curl_version" "7.36.0"
    version_result=$?
    if [ $version_result -le 1 ] && [ "$mode" = "normal" ]; then  # >= 7.36.0
        # 只在normal模式下使用这些选项，fallback模式避免使用
        if curl_supports_option "--no-alpn"; then
            options="$options --no-alpn"
        fi
        if curl_supports_option "--no-npn"; then
            options="$options --no-npn"
        fi
    fi

    echo "$options"
}

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

succ() {
    echo -e "${GREEN}[SUCC]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检测可用的netcat类工具
detect_netcat_tool() {
    if command -v nc >/dev/null 2>&1; then
        echo "nc"
    elif command -v netcat >/dev/null 2>&1; then
        echo "netcat"
    elif command -v ncat >/dev/null 2>&1; then
        echo "ncat"
    else
        echo ""
    fi
}

# 检测可用的端口检测工具
detect_port_tools() {
    
    local available_tools=()

    # 检查netcat类工具（统一处理）
    local netcat_tool=$(detect_netcat_tool)
    if [ -n "$netcat_tool" ]; then
        available_tools+=("netcat")  # 统一标识为netcat
    fi

    # 检查bash的/dev/tcp支持（测试连接到本地回环地址）
    if (echo >/dev/tcp/127.0.0.1/22) 2>/dev/null; then
        available_tools+=("bash_tcp")
        # 如果同时有timeout命令，添加timeout_bash方法
        if command -v timeout >/dev/null 2>&1; then
            available_tools+=("timeout_bash")
        fi
    elif (echo >/dev/tcp/127.0.0.1/80) 2>/dev/null; then
        # 尝试80端口（HTTP）
        available_tools+=("bash_tcp")
        if command -v timeout >/dev/null 2>&1; then
            available_tools+=("timeout_bash")
        fi
    fi

    # 检查curl的telnet支持
    if command -v curl >/dev/null 2>&1; then
        if curl --help 2>/dev/null | grep -q "telnet://"; then
            available_tools+=("curl_telnet")
        fi
    fi

    # 如果没有其他方法，尝试简单的bash重定向
    if [ ${#available_tools[@]} -eq 0 ]; then
        # 测试bash是否支持/dev/tcp（即使连接失败也算支持）
        if bash -c 'echo >/dev/tcp/127.0.0.1/99999' 2>/dev/null || [ $? -eq 1 ]; then
            available_tools+=("bash_simple")
        fi
    fi

    echo "${available_tools[@]}"
}

# 使用指定方法测试端口连通性
test_port_with_method() {

    # 参数: 方法名, 主机, 端口, 超时时间(可选，默认5秒)
    local method="$1"
    local host="$2"
    local port="$3"
    local timeout_val="${4:-5}"

    case "$method" in
        "netcat")
            # 自动选择可用的netcat工具
            local netcat_cmd=$(detect_netcat_tool)
            if [ -n "$netcat_cmd" ]; then
                $netcat_cmd -z -w"${timeout_val}" "${host}" "${port}" 2>/dev/null
            else
                return 1
            fi
            ;;
        "bash_tcp")
            if command -v timeout >/dev/null 2>&1; then
                timeout "${timeout_val}" bash -c "exec 3<>/dev/tcp/${host}/${port} && exec 3<&- && exec 3>&-" 2>/dev/null
            else
                bash -c "exec 3<>/dev/tcp/${host}/${port} && exec 3<&- && exec 3>&-" 2>/dev/null
            fi
            ;;
        "curl_telnet")
            curl -s --connect-timeout "${timeout_val}" --max-time "${timeout_val}" "telnet://${host}:${port}" </dev/null >/dev/null 2>&1
            ;;
        "bash_simple")
            (echo >/dev/tcp/"${host}"/"${port}") 2>/dev/null
            ;;
        "timeout_bash")
            timeout "${timeout_val}" bash -c "(echo >/dev/tcp/${host}/${port})" 2>/dev/null
            ;;
        *)
            return 1
            ;;
    esac
}

# 检查必要工具
check_dependencies() {
    
    info "检查必要的工具..."

    local missing_tools=()
    local critical_missing=false

    # 检查curl（必需工具）
    if ! command -v curl >/dev/null 2>&1; then
        missing_tools+=("curl")
        critical_missing=true
    else
        # 检查curl版本并提供兼容性信息
        local curl_version=$(get_curl_version)
        if [ -n "$curl_version" ]; then
            info "检测到curl版本: $curl_version"

            # 检查版本兼容性
            version_compare "$curl_version" "7.36.0"
            local version_result=$?
            if [ $version_result -eq 2 ]; then  # < 7.36.0
                warn "curl版本较旧，将使用兼容模式（不支持--no-alpn/--no-npn选项）"
            fi

            version_compare "$curl_version" "7.29.0"
            version_result=$?
            if [ $version_result -eq 2 ]; then  # < 7.29.0
                warn "curl版本过旧（< 7.29.0），可能存在兼容性问题"
            fi
        else
            warn "无法检测curl版本，将使用基础兼容选项"
        fi
    fi

    # 检查可用的端口检测工具
    local available_port_tools=($(detect_port_tools))

    if [ ${#available_port_tools[@]} -eq 0 ]; then
        missing_tools+=("端口检测工具(nc/netcat/bash-tcp)")
        critical_missing=true
        warn "未找到可用的端口检测工具"
        warn "支持的工具: nc, netcat, bash(/dev/tcp), curl(telnet模式)"
    else
        info "可用的端口检测工具: ${available_port_tools[*]}"
    fi

    # 如果有关键工具缺失，报错退出
    if [ "$critical_missing" = true ]; then
        error "缺少关键工具: ${missing_tools[*]}"
        error "请安装缺少的工具后重试"
        return 1
    fi

    # 如果只是非关键工具缺失，给出警告但继续
    if [ ${#missing_tools[@]} -ne 0 ]; then
        warn "部分工具缺失: ${missing_tools[*]}"
        warn "将使用备用方案继续检查"
    fi

    succ "工具依赖检查完成"
    return 0
}

# 检查网络连通性
check_network_connectivity() {

    info "检查网络连通性到 ${FIREWALL_HOST}..."

    # 使用ping检查基本连通性
    if ping -c "${PING_COUNT}" -W "${PING_TIMEOUT}" "${FIREWALL_HOST}" >/dev/null 2>&1; then
        succ "网络连通性正常"
        return 0
    else
        error "无法ping通目标主机 ${FIREWALL_HOST}"
        return 1
    fi
}

# 检查HTTPS端口连通性
check_https_port() {
    info "检查HTTPS端口(${FIREWALL_PORT})连通性..."

    # 获取可用的端口检测工具
    local available_tools=($(detect_port_tools))

    if [ ${#available_tools[@]} -eq 0 ]; then
        error "没有可用的端口检测工具"
        error "请安装 nc, netcat 或确保bash支持/dev/tcp"
        return 1
    fi

    # 定义检测方法的优先级顺序
    local preferred_order=("netcat" "bash_tcp" "timeout_bash" "curl_telnet" "bash_simple")

    # 按优先级尝试不同的检测方法
    for method in "${preferred_order[@]}"; do
        # 检查该方法是否可用
        local method_available=false
        for available in "${available_tools[@]}"; do
            if [ "$available" = "$method" ]; then
                method_available=true
                break
            fi
        done

        if [ "$method_available" = true ]; then
            info "使用 ${method} 方法检测端口连通性..."

            if test_port_with_method "$method" "${FIREWALL_HOST}" "${FIREWALL_PORT}" 5; then
                succ "HTTPS端口(${FIREWALL_PORT})连通正常"
                return 0
            else
                warn "${method} 方法检测失败，尝试下一种方法..."
            fi
        fi
    done

    error "HTTPS端口(${FIREWALL_PORT})连接失败"
    error "已尝试所有可用的检测方法: ${available_tools[*]}"
    return 1
}

# 构建curl基础选项
get_curl_options() {
    local curl_version=$(get_curl_version)

    if [ -z "$curl_version" ]; then
        warn "无法检测curl版本，使用基础兼容选项"
        curl_version="7.29.0"  # 假设最低版本
    fi

    local options=$(build_compatible_curl_options "$curl_version" "normal")

    # 添加额外的代理控制（兼容性考虑）
    options="$options --noproxy localhost --noproxy 127.0.0.1"

    echo "$options"
}

# 备用curl选项（用于故障恢复）
get_curl_options_fallback() {
    local curl_version=$(get_curl_version)

    if [ -z "$curl_version" ]; then
        curl_version="7.29.0"  # 假设最低版本
    fi

    local options=$(build_compatible_curl_options "$curl_version" "fallback")

    echo "$options"
}

# 禁用代理环境变量的函数
disable_proxy() {

    # 禁用所有代理设置
    unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY
    export no_proxy="*"
    export NO_PROXY="*"
}

# 检查Web服务响应
check_web_response() {

    info "检查Web服务响应..."

    # 禁用代理设置
    disable_proxy

    # 创建临时文件存储响应
    local temp_file=$(mktemp)
    local cookie_jar=$(mktemp)
    local curl_options=$(get_curl_options)

    # 发送HTTP请求检查响应（跟随重定向到login.html）
    local http_code=$(curl -s ${curl_options} -w "%{http_code}" \
        --connect-timeout "${TIMEOUT}" \
        --max-time "${TIMEOUT}" \
        --user-agent "${USER_AGENT}" \
        --cookie-jar "${cookie_jar}" \
        -o "${temp_file}" \
        "${FIREWALL_URL}" 2>/dev/null)

    # 如果主要方法失败，尝试备用方法
    if [ "${http_code}" = "000" ] || [ -z "${http_code}" ]; then
        warn "主要curl配置失败，尝试备用配置..."
        local fallback_options=$(get_curl_options_fallback)
        http_code=$(curl -s ${fallback_options} -w "%{http_code}" \
            --connect-timeout "${TIMEOUT}" \
            --max-time "${TIMEOUT}" \
            --user-agent "${USER_AGENT}" \
            --cookie-jar "${cookie_jar}" \
            -o "${temp_file}" \
            "${FIREWALL_URL}" 2>/dev/null)
    fi

    # 检查响应结果
    if [ "${http_code}" = "200" ] || [ "${http_code}" = "302" ] || [ "${http_code}" = "301" ]; then

        # 检查响应内容是否包含login.html或登录相关内容
        if grep -qi "login\.html\|login\|password\|username\|用户名\|密码" "${temp_file}"; then
            succ "Web服务响应正常，已重定向到登录页面 (HTTP ${http_code})"
        else
            succ "Web服务响应正常 (HTTP ${http_code})"
        fi
        rm -f "${temp_file}" "${cookie_jar}"
        return 0
    else
        error "Web服务响应异常 (HTTP ${http_code})"
        error "请检查网络连接、防火墙设置或SSL配置"
        rm -f "${temp_file}" "${cookie_jar}"
        return 1
    fi
}

# 检查AFW3000重定向流程
check_afw3000_redirect_flow() {

    info "检查AFW3000重定向流程 (/ -> /login.html)..."

    # 禁用代理设置
    disable_proxy

    local temp_file=$(mktemp)
    local cookie_jar=$(mktemp)
    local curl_options=$(get_curl_options)

    # 访问根路径，检查是否自动重定向到login.html
    local response=$(curl -s ${curl_options} -w "%{http_code}|%{redirect_url}|%{url_effective}" \
        --connect-timeout "${TIMEOUT}" \
        --max-time "${TIMEOUT}" \
        --user-agent "${USER_AGENT}" \
        --cookie-jar "${cookie_jar}" \
        -o "${temp_file}" \
        "${FIREWALL_URL}")

    local http_code=$(echo "$response" | cut -d'|' -f1)
    local redirect_url=$(echo "$response" | cut -d'|' -f2)
    local effective_url=$(echo "$response" | cut -d'|' -f3)

    # 检查重定向结果
    if [ "${http_code}" = "200" ]; then

        # 检查是否直接访问了login.html或其内容
        if echo "${effective_url}" | grep -q "login\.html"; then
            succ "AFW3000重定向流程正常: / -> /login.html"
            rm -f "${temp_file}" "${cookie_jar}"
            return 0
        elif grep -qi "login\.html\|login\|password\|username" "${temp_file}"; then
            succ "AFW3000重定向流程正常: 检测到登录页面内容"
            rm -f "${temp_file}" "${cookie_jar}"
            return 0
        else
            warn "访问根路径成功但未检测到预期的login.html重定向"
            rm -f "${temp_file}" "${cookie_jar}"
            return 1
        fi
    elif [ "${http_code}" = "302" ] || [ "${http_code}" = "301" ]; then
        if echo "${redirect_url}" | grep -q "login\.html"; then
            succ "AFW3000重定向流程正常: HTTP ${http_code} -> ${redirect_url}"
            rm -f "${temp_file}" "${cookie_jar}"
            return 0
        else
            warn "检测到重定向但目标不是login.html: ${redirect_url}"
            rm -f "${temp_file}" "${cookie_jar}"
            return 1
        fi
    else
        error "AFW3000重定向流程检查失败 (HTTP ${http_code})"
        rm -f "${temp_file}" "${cookie_jar}"
        return 1
    fi
}

# 检查登录页面
check_login_page() {

    info "检查AFW3000登录页面 (/login.html)..."

    # 禁用代理设置
    disable_proxy

    local temp_file=$(mktemp)
    local cookie_jar=$(mktemp)
    local curl_options=$(get_curl_options)

    # 直接访问login.html页面
    local login_url="${FIREWALL_URL}/login.html"
    local http_code=$(curl -s ${curl_options} -w "%{http_code}" \
        --connect-timeout "${TIMEOUT}" \
        --max-time "${TIMEOUT}" \
        --user-agent "${USER_AGENT}" \
        --cookie-jar "${cookie_jar}" \
        -o "${temp_file}" \
        "${login_url}")

    if [ "${http_code}" = "200" ]; then
        # 检查页面内容是否包含AFW3000登录相关元素
        if grep -qi "login\|password\|username\|用户名\|密码\|admin\|登录" "${temp_file}"; then
            succ "AFW3000登录页面 (/login.html) 加载正常"
            rm -f "${temp_file}" "${cookie_jar}"
            return 0
        else
            warn "login.html页面加载成功但未检测到登录表单"
            rm -f "${temp_file}" "${cookie_jar}"
            return 1
        fi
    else
        error "AFW3000登录页面加载失败 (HTTP ${http_code})"
        rm -f "${temp_file}" "${cookie_jar}"
        return 1
    fi
}

# AFW3000专用登录验证函数
attempt_afw3000_login() {

    info "模拟AFW3000真实登录流程"

    # 禁用代理设置
    disable_proxy

    local temp_file=$(mktemp)
    local cookie_jar=$(mktemp)
    local curl_options=$(get_curl_options)
    local login_success=false

    # 步骤1: 访问根路径，获取初始session和cookie
    info "步骤1: 访问根路径 ${FIREWALL_URL}"
    curl -s ${curl_options} \
        --connect-timeout "${TIMEOUT}" \
        --max-time "${TIMEOUT}" \
        --user-agent "${USER_AGENT}" \
        --cookie-jar "${cookie_jar}" \
        -o /dev/null \
        "${FIREWALL_URL}"

    # 步骤2: 访问login.html页面，获取登录表单
    info "步骤2: 访问登录页面 /login.html"
    local login_page_url="${FIREWALL_URL}/login.html"
    curl -s ${curl_options} \
        --connect-timeout "${TIMEOUT}" \
        --max-time "${TIMEOUT}" \
        --user-agent "${USER_AGENT}" \
        --cookie "${cookie_jar}" \
        --cookie-jar "${cookie_jar}" \
        -o "${temp_file}" \
        "${login_page_url}"

    # 步骤3: 提交登录表单
    info "步骤3: 提交登录凭据 (用户名: ${USERNAME}，密码: ${PASSWORD})"

    # AFW3000常用的登录参数组合
    local login_params_list=(
        "username=${USERNAME}&password=${PASSWORD}"
        "user=${USERNAME}&pass=${PASSWORD}"
        "admin_name=${USERNAME}&admin_pass=${PASSWORD}"
        "login_username=${USERNAME}&login_password=${PASSWORD}"
        "name=${USERNAME}&pwd=${PASSWORD}"
    )

    # 尝试不同的登录提交路径
    local login_submit_urls=(
        "${FIREWALL_URL}/login.html"
        "${FIREWALL_URL}/login"
        "${FIREWALL_URL}/auth"
        "${FIREWALL_URL}/"
    )

    local consecutive_failures=0
    local max_consecutive_failures=3

    for submit_url in "${login_submit_urls[@]}"; do
        for params in "${login_params_list[@]}"; do

            info "\t 尝试提交到: ${submit_url}"

            local login_response=$(curl -s ${curl_options} -w "%{http_code}" \
                --connect-timeout "${TIMEOUT}" \
                --max-time "${TIMEOUT}" \
                --user-agent "${USER_AGENT}" \
                --cookie "${cookie_jar}" \
                --cookie-jar "${cookie_jar}" \
                --referer "${login_page_url}" \
                -d "${params}" \
                -X POST \
                -o "${temp_file}" \
                "${submit_url}" 2>/dev/null)

            # 检查是否为网络错误（连续失败检测）
            if [ "${login_response}" = "000" ] || [ -z "${login_response}" ]; then
                consecutive_failures=$((consecutive_failures + 1))
                if [ $consecutive_failures -ge $max_consecutive_failures ]; then
                    error "\t 连续网络失败 ${consecutive_failures} 次，停止登录尝试"
                    break 2
                fi
                continue
            else
                consecutive_failures=0  # 重置连续失败计数
            fi

            # 检查登录响应
            if [ "${login_response}" = "200" ] || [ "${login_response}" = "302" ] || [ "${login_response}" = "301" ]; then

                # 检查是否重定向到webui主页面
                if grep -qiE "webui.*g=main|/webui/\?g=main" "${temp_file}" 2>/dev/null; then
                    succ "\t 登录成功！检测到重定向至 /webui/?g=main"
                    login_success=true
                    break 2
                elif grep -qiE "${SUCCESS_KEYWORDS}" "${temp_file}" 2>/dev/null; then
                    succ "\t 登录验证成功 (检测到成功关键词)"
                    login_success=true
                    break 2
                elif grep -qiE "${FAILURE_KEYWORDS}" "${temp_file}" 2>/dev/null; then
                    warn "\t 登录失败 (检测到失败关键词)"
                    continue
                fi
            fi
        done

        if [ "$login_success" = true ]; then
            break
        fi
    done

    # 步骤4: 如果登录成功，尝试访问主管理界面验证
    if [ "$login_success" = true ]; then

        info "步骤4: 验证访问主管理界面 /webui/?g=main"
        local main_page_url="${FIREWALL_URL}/webui/?g=main"
        local main_response=$(curl -s ${curl_options} -w "%{http_code}" \
            --connect-timeout "${TIMEOUT}" \
            --max-time "${TIMEOUT}" \
            --user-agent "${USER_AGENT}" \
            --cookie "${cookie_jar}" \
            -o "${temp_file}" \
            "${main_page_url}" 2>/dev/null)

        if [ "${main_response}" = "200" ]; then
            succ "\t 成功访问AFW3000主管理界面"
        else
            warn "\t 登录成功但无法访问主管理界面 (HTTP ${main_response})"
        fi
    fi

    # 清理临时文件
    rm -f "${temp_file}" "${cookie_jar}"

    if [ "$login_success" = true ]; then
        return 0
    else
        error "AFW3000登录验证失败 - 请检查凭据或网络连接"
        return 1
    fi
}

# 通用登录验证函数（保持向后兼容）
attempt_login() {

    # 首先尝试AFW3000专用登录流程
    if attempt_afw3000_login; then
        return 0
    fi

    # 如果AFW3000专用流程失败，检查是否应该跳过通用流程
    # 如果基础网络检查都失败了，就不要浪费时间在登录验证上
    if [ "$overall_status" -ne 0 ]; then
        warn "由于基础网络检查失败，跳过通用登录流程验证"
        return 1
    fi

    # 如果AFW3000专用流程失败，回退到通用流程
    warn "AFW3000专用登录流程失败，尝试通用登录流程..."

    # 禁用代理设置
    disable_proxy

    local temp_file=$(mktemp)
    local cookie_jar=$(mktemp)
    local curl_options=$(get_curl_options)
    local login_success=false

    # 首先获取登录页面以获取可能的CSRF token或session
    curl -s ${curl_options} \
        --connect-timeout "${TIMEOUT}" \
        --max-time "${TIMEOUT}" \
        --user-agent "${USER_AGENT}" \
        --cookie-jar "${cookie_jar}" \
        -o /dev/null \
        "${FIREWALL_URL}"

    # 尝试不同的登录路径
    for login_path in ${LOGIN_PATHS}; do
        info "尝试通用登录路径: ${login_path}"

        local login_url="${FIREWALL_URL}${login_path}"

        # 尝试多种参数组合
        for params in "username=${USERNAME}&password=${PASSWORD}" \
                     "user=${USERNAME}&pass=${PASSWORD}" \
                     "login_username=${USERNAME}&login_password=${PASSWORD}" \
                     "admin_name=${USERNAME}&admin_pass=${PASSWORD}"; do

            local login_response=$(curl -s ${curl_options} -w "%{http_code}" \
                --connect-timeout "${TIMEOUT}" \
                --max-time "${TIMEOUT}" \
                --user-agent "${USER_AGENT}" \
                --cookie "${cookie_jar}" \
                --cookie-jar "${cookie_jar}" \
                -d "${params}" \
                -X POST \
                -o "${temp_file}" \
                "${login_url}" 2>/dev/null)

            # 检查登录结果
            if [ "${login_response}" = "200" ] || [ "${login_response}" = "302" ] || [ "${login_response}" = "301" ]; then
                if grep -qiE "${SUCCESS_KEYWORDS}" "${temp_file}" 2>/dev/null; then
                    succ "通用登录验证成功 (路径: ${login_path})"
                    login_success=true
                    break 2
                elif grep -qiE "${FAILURE_KEYWORDS}" "${temp_file}" 2>/dev/null; then
                    warn "通用登录失败 (路径: ${login_path}, 参数: ${params%%&*}...)"
                    continue
                fi
            fi
        done

        if [ "$login_success" = true ]; then
            break
        fi
    done

    # 清理临时文件
    rm -f "${temp_file}" "${cookie_jar}"

    if [ "$login_success" = true ]; then
        return 0
    else
        error "所有登录尝试均失败 - 请检查凭据或登录接口"
        return 1
    fi
}

# 主函数
main() {
    echo
    echo "=============================================="
    echo "AFW3000防火墙Web服务可用性检查"
    echo "目标设备: ${FIREWALL_HOST}:${FIREWALL_PORT}"
    echo "检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=============================================="
    echo

    local overall_status=0

    # 执行各项检查
    check_dependencies || overall_status=1
    echo

    check_network_connectivity || overall_status=1
    echo

    check_https_port || overall_status=1
    echo

    check_web_response || overall_status=1
    echo

    check_afw3000_redirect_flow || overall_status=1
    echo

    check_login_page || overall_status=1
    echo

    attempt_login
    local login_status=$?
    if [ $login_status -eq 1 ]; then
        overall_status=1
    fi
    echo

    # 输出总结
    echo "=============================================="
    if [ $overall_status -eq 0 ]; then
        succ "所有检查项目通过 - AFW3000 Web服务运行正常"
    else
        error "部分检查项目失败 - 请检查上述错误信息"
    fi
    echo "=============================================="

    exit $overall_status
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
