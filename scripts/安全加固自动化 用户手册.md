# 工具介绍

## 简介

**安全加固**

安全加固是配置和优化信息系统的过程，旨在消除已知漏洞并增强系统的安全性。它涵盖了网络层、主机层、软件层和应用层等多个层次，通过打补丁、强化账号安全、修改安全配置、优化访问控制策略等手段，建立符合安全需求的安全状态，从而提升系统的整体防护能力。

**安全加固执行工具**

主要用于安全加固脚本批量执行进行调度（目前已兼容mysql、linux相关脚本），比如编排脚本执行顺序，记录过程日志，一键批量执行等。

## 基本模块

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-26-40-image.png)

# 工具使用

## 执行脚本

> **1、上传压缩包并解压**

将压缩包 `secure_check.tar.gz` 上传到要加固的机器后解压。

```sh
# 压缩包解压
tar zxvf secure_check.tar.gz`
# 进入执行目录
cd secure_check
```

解压后目录结构：

```bash
secure_check # 工具目录
├── backup # 备份文件目录
│   ├── debug # 手动调试备份目录
│   │   └── Linux-xxx # 调试脚本编号
│   │       ├── backup_records.txt # 备份记录
│   │       └── <backupFileName>.bak.202411061649xx # 备份文件，格式：备份文件名.bak。时间戳
│   └── stand # 工具执行备份目录
│       ├── database # 数据库加固备份目录
│       │   ├── 20241112163509 # 备份版本目录
│       │   │   └── <backupFileName>.bak.202411061649xx
│       │   └── backup_records.txt
│       └── system # 操作系统加固备份目录
│           ├── 20241106175616
│           │   └── <backupFileName>.bak.202411061649xx
│           └── backup_records.txt
├── config # 执行配置目录
│   ├── backup_restore.yml # 备份回滚重载配置
│   ├── database_scripts.yml # 数据库加固执行配置
│   └── system_scripts.yml # 操作系统加固执行配置
├── lib # 加固脚本依赖库目录
│   ├── base.sh # 公共基础库
│   ├── db_lib.sh
│   ├── exec.sh # 脚本执行相关
│   ├── log.sh
│   ├── mysql.sh # mysql执行相关
│   ├── os_lib.sh
│   └── os.sh # 操作系统执行相关
├── log # 执行日志目录
│   ├── exec_shell.log # secure-guard 执行过程日志
│   └── output.log # 脚本执行输出日志
├── scripts # 加固脚本目录
│   ├── database # 数据库加固脚本
│   │   ├── auth.txt # 数据库连接认证配置
│   │   ├── debug.sh
│   │   ├── lib.sh -> ../../lib/db_lib.sh
│   │   ├── ...
│   │   └── Mysql-xxx.sh
│   ├── middleware # 中间件加固脚本
│   └── system # 操作系统加固脚本
│       ├── debug.sh
│       ├── lib.sh -> ../../lib/os_lib.sh
│       ├── ...
│       └── Linux-xxx.sh
└── secure-guard # 框架加固执行入口
```

> **2、加固执行前配置**

脚本配置为 `config` 目录下，命名为 _scripts.yml 的配置，主要设置执行脚本白名单及是否允许自动加固，设置完毕后即可正常使用加固执行框架。

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-38-46-image.png)

<mark>【注意】</mark>

* 配置中脚本的排列顺序，表示实际执行时的顺序，执行顺序为由上往下依次执行，其中配置里脚本顺序已经预先编排好了，故直接执行即可。
* 加固脚本手动执行时会进行是否修复询问，然后根据用户输入决定是否进行修复，这块在批量执行工具中则是通过 allow 参数进行控制，true 表示同意修复，false表示不同意，即只检查但不修复。

> **3、执行脚本**

脚本执行入口为 `secure-guard`，通过指定不同参数控制执行方式，具体参数可以使用 `./secure-guard -h` 进行查看。

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-44-40-image.png)

* **批量执行脚本（默认）**

命令格式：`./secure-guard -t 加固类型`

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-46-19-image.png)

<mark>【注意】</mark>

1. 当不指定具体执行脚本时，则为批量执行模式，执行框架会根据读取的脚本配置，按配置里自上往下的顺序执行加固脚本，现场加固时直接按预先设置好的加固顺序执行即可。

2. 执行批量加固前，要保留一个，以防加固后出现问题无法登陆。
* **执行指定脚本**

命令格式：`./secure-guard -t 加固类型 -s 脚本名称`![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-48-57-image.png)

<mark>【注意】</mark>指定脚本必须已在脚本配置白名单中，非白名单中脚本指定无效。

* **手动执行脚本**

命令格式：`cd scripts/对应脚本目录 && bash 脚本名称`

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-52-15-image.png)

<mark>【注意】</mark>手动执行时必须先进到脚本所在目录下执行才有效，因为会做执行依赖校验。

## 备份回滚

* **执行指定脚本**

命令格式：`./secure-guard -t 加固类型 -l`

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-55-30-image.png)

- **根据指定版本号回滚**

命令格式：`./secure-guard -t 加固类型 -r 备份版本号`

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-56-33-image.png)

- **根据指定版本序号回滚**

命令格式：`./secure-guard -t 加固类型 -r 备份版本序号`

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-56-41-image.png)

【特殊用法】如果备份版本序号为0，则表示回滚至最近一次备份版本。

![](C:\Users\<USER>\AppData\Roaming\marktext\images\2024-12-18-14-57-21-image.png)



# 常见问题

> **1、安全加固后无法连接root**

安全加固会禁止使用root远程登录，但在禁用同时会更创建ydsoc用户作为远程登录代替，所以所以请使用ydsoc连接设备后在切换root。

> **2、加固后 ydsoc 密码连接失败**

应生产部门要求，ydsoc 加固后统一进行密码初始化，目前最新初始密码为 `Yd@36863`，采用初始化后新密码登录即可。

> **3、ydsoc通过su切换root失败**

安全加固后会限制只有在管理组的用户可以切换root，如 centos 默认管理组是wheel，则加固后，ydosc必须先加入wheel组后才能切换到root。

* redhat类系统默认管理组：`wheel` （如 centos，uos，中标麒麟 等）

* ubantu类系统默认管理组：`sudo` （如 银河麒麟 ）

也有其他系统可能采用自定义管理组，具体以实际系统配置为准。

**【补充】** 将ydsoc加入wheel管理组命令：`usermod -aG wheel ydsoc`



目前该工具仍在持续完善中，相关问题或建议可联系生产测试部 郑重其 反馈，感谢。