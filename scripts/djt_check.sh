#!/bin/bash

# watchdog 检查
if tail /root/secure-tools/watchdog/feed-dog.log >/dev/null 2>&1 || tail /root/watchdog/feed-dog.log >/dev/null 2>&1;then
	echo -e "\033[32m watchdog 安装成功 \033[0m"
else
	echo -e "\033[31m watchdog 安装失败 \033[0m"
fi

# nmap 检查
if type nmap >/dev/null 2>&1;then
	echo -e "\033[32m nmap 安装成功 \033[0m"
else
	echo -e "\033[31m nmap 安装失败 \033[0m"
fi

# wireguard 检查
scpserver_intsall=0
if systemctl status scpserver >/dev/null 2>&1;then
	scpserver_intsall=1
fi

wgtool_intsall=0
if wg --help >/dev/null 2>&1;then
	wgtool_intsall=1
fi

wgmodule_intsall=0
if [ -f /etc/wireguard/wg_sm.conf ];then
	wgmodule_intsall=1
fi

if [[ $scpserver_intsall -eq 1 ]] && [[ $wgtool_intsall -eq 1 ]] && [[ $wgmodule_intsall -eq 1 ]];then
	echo -e "\033[32m wireguard 安装成功 \033[0m"
else
	echo -e "\033[31m wireguard 安装失败 \033[0m"
fi

# salt 检查
if systemctl status ydsomn-minion >/dev/null 2>&1;then
	echo -e "\033[32m salt 安装成功 \033[0m"
else
	echo -e "\033[31m salt 安装失败 \033[0m"
fi

# 安全代理 检查
if /home/<USER>/bin/admin.sh show >/dev/null 2>&1;then
	echo -e "\033[32m 安全代理 安装成功 \033[0m"
else
	echo -e "\033[31m 安全代理 安装失败 \033[0m"
fi

# zabbix 检查
if systemctl status ydMonitorAgent >/dev/null 2>&1;then
	echo -e "\033[32m zabbix agent 安装成功 \033[0m"
else
	echo -e "\033[31m zabbix agent 安装失败 \033[0m"
fi

# socket5 检查
if systemctl status 3proxy.service >/dev/null 2>&1;then
	echo -e "\033[32m socket5 安装成功 \033[0m"
else
	echo -e "\033[31m socket5 安装失败 \033[0m"
fi

# svn 检查
if which svn >/dev/null 2>&1;then
	echo -e "\033[32m svn 安装成功 \033[0m"
else
	echo -e "\033[31m svn 安装失败 \033[0m"
fi

# sysdig check
if sysdig --version >/dev/null 2>&1;then
	echo -e "\033[32m sysdig 安装成功 \033[0m"
else
	echo -e "\033[31m sysdig 安装失败 \033[0m"
fi

# jenkins 检查
java_version=$(java --version | head -n 1 | awk '{print $2}')
if [[ $java_version == "21.0.4" ]];then
	echo -e "\033[32m jenkins jdk 安装成功 \033[0m"
else
	echo -e "\033[31m jenkins jdk 安装失败 \033[0m"
fi

# beep 检查
if /usr/local/socrelay/bin/beeprelay -? >/dev/null 2>&1;then
    echo -e "\033[32m beep 安装成功 \033[0m"
else
    echo -e "\033[31m beep 安装失败 \033[0m"
fi


