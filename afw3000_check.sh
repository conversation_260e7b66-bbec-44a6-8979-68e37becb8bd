#!/bin/bash
# 版本: 1.0
# 作者: dkyzheng
# 描述: AFW3000防火墙设备检测统一入口

# =============================================================================
# 脚本初始化
# =============================================================================

# 设置脚本目录和工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 脚本信息
SCRIPT_NAME="AFW3000防火墙检测工具"
SCRIPT_VERSION="1.0"
SCRIPT_DATE=$(date +"%Y-%m-%d %H:%M:%S")

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    log_error "无法找到SSH工具库 lib/ssh_utils.sh"
    exit 1
fi

# =============================================================================
# 全局变量
# =============================================================================

# 检测目标节点
TARGET_NODES=""
ALL_NODES=("n1" "n2" "n3")

# 本地检测模式标志
LOCAL_MODE=false

# 报告相关
REPORT_DIR="report"
REPORT_FILE=""
START_TIME=""
RAW_DATA_DIR="/tmp/afw3000_raw_data_$$"

# =============================================================================
# 帮助信息和使用说明
# =============================================================================

show_help() {
    cat <<EOF

========================================
$SCRIPT_NAME v$SCRIPT_VERSION
========================================

用法: $0 [选项] <目标节点>

目标节点:
  all       检测所有节点（N1、N2、N3）
  n1        仅检测N1节点（主节点，默认安博通防火墙）
  n2        仅检测N2节点（协节点）
  n3        仅检测N3节点（协节点）

选项:
  -h, --help     显示此帮助信息
  -v, --version  显示版本信息
  --debug        启用调试模式
  --local        启用本地检测模式（检测当前主机）

使用示例:
  $0 all         # 检测所有节点
  $0 n1          # 仅检测N1节点
  $0 n2          # 仅检测N2节点
  $0 n3          # 仅检测N3节点
  $0 --debug n1  # 调试模式检测N1节点
  $0 --local     # 本地检测模式（检测当前主机）
  $0 --local --debug  # 本地检测+调试模式

检测内容:
- 网络连通性检测
- 硬件信息检测（配置）
- 系统信息检测（配置，状态）
- 软件信息检测（配置，状态）

输出:
- 报告保存目录：report/
- 报告名格式: afw3000_check_YYYYMMDD_HHMMSS.txt

EOF
}

show_version() {
    cat <<EOF
版本: $SCRIPT_VERSION
EOF
}

# =============================================================================
# 参数解析和验证
# =============================================================================

parse_arguments() {
    local debug_mode=false

    # 如果没有参数，显示帮助信息
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
        -h | --help)
            show_help
            exit 0
            ;;
        -v | --version)
            show_version
            exit 0
            ;;
        --debug)
            debug_mode=true
            log_info "启用调试模式"
            shift
            ;;
        --local)
            LOCAL_MODE=true
            TARGET_NODES="localhost"
            log_info "启用本地检测模式"
            shift
            ;;
        all)
            TARGET_NODES="n1 n2 n3"
            log_info "检测目标: 所有节点 (N1, N2, N3)"
            shift
            ;;
        n1 | n2 | n3)
            TARGET_NODES="$1"
            log_info "检测目标: $1节点"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            echo ""
            show_help
            exit 1
            ;;
        esac
    done

    # 验证目标节点是否已设置
    if [[ -z "$TARGET_NODES" ]]; then
        log_error "未指定检测目标节点"
        echo ""
        show_help
        exit 1
    fi

    # 设置调试模式
    if [[ "$debug_mode" == true ]]; then
        set -x # 启用命令跟踪
    fi
}

# =============================================================================
# 初始化和环境检查
# =============================================================================

initialize_environment() {
    log_info "初始化检测环境..."

    # 记录开始时间
    START_TIME=$(date +"%Y%m%d_%H%M%S")

    # 创建报告目录
    if [[ ! -d "$REPORT_DIR" ]]; then
        mkdir -p "$REPORT_DIR"
        log_info "创建报告目录: $REPORT_DIR"
    fi

    # 本地模式下跳过设备配置加载和SSH依赖检查
    if [[ "$LOCAL_MODE" != "true" ]]; then
        # 加载配置文件
        if ! load_config "config/devices.conf"; then
            log_error "设备配置文件加载失败"
            exit 1
        fi

        if ! load_config "config/check_config.conf"; then
            log_error "检测配置文件加载失败"
            exit 1
        fi

        # 检查依赖工具
        if ! check_dependencies; then
            log_error "依赖工具检查失败"
            exit 1
        fi
    else
        log_info "本地检测模式，跳过远程配置加载和SSH依赖检查"
    fi

    log_info "环境初始化完成"
}

# =============================================================================
# 检测执行逻辑
# =============================================================================

# 本地检测执行函数
execute_local_detection() {
    log_info "开始本地检测..."

    # 获取本地主机信息
    local hostname=$(hostname)
    local local_ip=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}' 2>/dev/null || echo "127.0.0.1")

    log_info "本地主机信息: $hostname ($local_ip)"

    # 硬件检测模块（本地模式）
    log_info "执行硬件检测..."
    local hardware_check_result=""
    local hardware_check_output=""

    # 调用硬件检测模块并捕获输出
    hardware_check_output=$(./modules/hardware_check.sh localhost "$local_ip" "$(whoami)" "" 2>&1)
    local hardware_exit_code=$?

    # 将硬件检测结果保存到临时文件，供报告生成使用
    local temp_hardware_file="/tmp/afw3000_hardware_result_localhost_$$"
    echo "HARDWARE_CHECK_EXIT_CODE=$hardware_exit_code" >"$temp_hardware_file"
    echo "HARDWARE_CHECK_OUTPUT_START" >>"$temp_hardware_file"
    echo "$hardware_check_output" >>"$temp_hardware_file"
    echo "HARDWARE_CHECK_OUTPUT_END" >>"$temp_hardware_file"

    if [[ $hardware_exit_code -eq 0 ]]; then
        log_info "本地硬件检测成功"
        hardware_check_result="成功"
    else
        log_warn "本地硬件检测失败"
        hardware_check_result="失败"
    fi

    # 系统信息检测模块（本地模式）
    log_info "执行系统信息检测..."
    local system_check_result=""
    local system_check_output=""

    # 调用系统信息检测模块并捕获输出
    system_check_output=$(./modules/system_check.sh localhost "$local_ip" "$(whoami)" "" 22 2>&1)
    local system_exit_code=$?

    # 将系统信息检测结果保存到临时文件，供报告生成使用
    local temp_system_file="/tmp/afw3000_system_result_localhost_$$"
    echo "SYSTEM_CHECK_EXIT_CODE=$system_exit_code" >"$temp_system_file"
    echo "SYSTEM_CHECK_OUTPUT_START" >>"$temp_system_file"
    echo "$system_check_output" >>"$temp_system_file"
    echo "SYSTEM_CHECK_OUTPUT_END" >>"$temp_system_file"

    if [[ $system_exit_code -eq 0 ]]; then
        log_info "本地系统信息检测成功"
        system_check_result="成功"
    else
        log_warn "本地系统信息检测失败"
        system_check_result="失败"
    fi

    # 软件信息检测模块（本地模式）
    log_info "执行软件信息检测..."
    local software_check_result=""
    local software_check_output=""

    # 调用软件信息检测模块并捕获输出
    software_check_output=$(./modules/software_check.sh localhost "$local_ip" "$(whoami)" "" 22 2>&1)
    local software_exit_code=$?

    # 将软件信息检测结果保存到临时文件，供报告生成使用
    local temp_software_file="/tmp/afw3000_software_result_localhost_$$"
    echo "SOFTWARE_CHECK_EXIT_CODE=$software_exit_code" >"$temp_software_file"
    echo "SOFTWARE_CHECK_OUTPUT_START" >>"$temp_software_file"
    echo "$software_check_output" >>"$temp_software_file"
    echo "SOFTWARE_CHECK_OUTPUT_END" >>"$temp_software_file"

    if [[ $software_exit_code -eq 0 ]]; then
        log_info "本地软件信息检测成功"
        software_check_result="成功"
    else
        log_warn "本地软件信息检测失败"
        software_check_result="失败"
    fi

    log_info "本地检测完成 (硬件检测: $hardware_check_result, 系统信息检测: $system_check_result, 软件信息检测: $software_check_result)"

    return 0
}

execute_detection() {
    local target_node="$1"

    # 本地检测模式
    if [[ "$LOCAL_MODE" == "true" ]]; then
        execute_local_detection
        return $?
    fi

    log_info "开始检测 $target_node 节点..."

    # 获取节点配置
    local node_config
    node_config=$(get_device_config "$target_node")
    if [[ $? -ne 0 ]]; then
        log_error "$target_node 节点配置获取失败"
        return 1
    fi

    # 解析节点配置
    local host user password port backup_user backup_password
    read -r host user password port backup_user backup_password <<<"$node_config"

    log_info "$target_node 节点信息: $user@$host:$port"

    # 网络检测模块
    log_info "执行网络检测..."
    local network_check_result=""
    local network_check_output=""

    # 调用网络检测模块并捕获输出
    network_check_output=$(./modules/network_check.sh "$target_node" "$host" "$user" "$password" 2>&1)
    local network_exit_code=$?

    # 将网络检测结果保存到临时文件，供报告生成使用
    local temp_result_file="/tmp/afw3000_network_result_${target_node}_$$"
    echo "NETWORK_CHECK_EXIT_CODE=$network_exit_code" >"$temp_result_file"
    echo "NETWORK_CHECK_OUTPUT_START" >>"$temp_result_file"
    echo "$network_check_output" >>"$temp_result_file"
    echo "NETWORK_CHECK_OUTPUT_END" >>"$temp_result_file"

    if [[ $network_exit_code -eq 0 ]]; then
        log_info "$target_node 节点网络检测成功"
        network_check_result="成功"
    else
        log_error "$target_node 节点网络检测失败"
        network_check_result="失败"
        return 1
    fi

    # 硬件检测模块（在网络检测成功后执行）
    log_info "执行硬件检测..."
    local hardware_check_result=""
    local hardware_check_output=""

    # 调用硬件检测模块并捕获输出
    hardware_check_output=$(./modules/hardware_check.sh "$target_node" "$host" "$user" "$password" 2>&1)
    local hardware_exit_code=$?

    # 将硬件检测结果保存到临时文件，供报告生成使用
    local temp_hardware_file="/tmp/afw3000_hardware_result_${target_node}_$$"
    echo "HARDWARE_CHECK_EXIT_CODE=$hardware_exit_code" >"$temp_hardware_file"
    echo "HARDWARE_CHECK_OUTPUT_START" >>"$temp_hardware_file"
    echo "$hardware_check_output" >>"$temp_hardware_file"
    echo "HARDWARE_CHECK_OUTPUT_END" >>"$temp_hardware_file"

    if [[ $hardware_exit_code -eq 0 ]]; then
        log_info "$target_node 节点硬件检测成功"
        hardware_check_result="成功"
    else
        log_warn "$target_node 节点硬件检测失败"
        hardware_check_result="失败"
        # 硬件检测失败不影响整体流程，继续执行
    fi

    # 系统信息检测模块（在硬件检测之后执行）
    log_info "执行系统信息检测..."
    local system_check_result=""
    local system_check_output=""

    # 调用系统信息检测模块并捕获输出
    system_check_output=$(./modules/system_check.sh "$target_node" "$host" "$user" "$password" "$port" 2>&1)
    local system_exit_code=$?

    # 将系统信息检测结果保存到临时文件，供报告生成使用
    local temp_system_file="/tmp/afw3000_system_result_${target_node}_$$"
    echo "SYSTEM_CHECK_EXIT_CODE=$system_exit_code" >"$temp_system_file"
    echo "SYSTEM_CHECK_OUTPUT_START" >>"$temp_system_file"
    echo "$system_check_output" >>"$temp_system_file"
    echo "SYSTEM_CHECK_OUTPUT_END" >>"$temp_system_file"

    if [[ $system_exit_code -eq 0 ]]; then
        log_info "$target_node 节点系统信息检测成功"
        system_check_result="成功"
    else
        log_warn "$target_node 节点系统信息检测失败"
        system_check_result="失败"
        # 系统信息检测失败不影响整体流程，继续执行
    fi

    # 软件信息检测模块（在系统信息检测之后执行）
    log_info "执行软件信息检测..."
    local software_check_result=""
    local software_check_output=""

    # 调用软件信息检测模块并捕获输出
    software_check_output=$(./modules/software_check.sh "$target_node" "$host" "$user" "$password" "$port" 2>&1)
    local software_exit_code=$?

    # 将软件信息检测结果保存到临时文件，供报告生成使用
    local temp_software_file="/tmp/afw3000_software_result_${target_node}_$$"
    echo "SOFTWARE_CHECK_EXIT_CODE=$software_exit_code" >"$temp_software_file"
    echo "SOFTWARE_CHECK_OUTPUT_START" >>"$temp_software_file"
    echo "$software_check_output" >>"$temp_software_file"
    echo "SOFTWARE_CHECK_OUTPUT_END" >>"$temp_software_file"

    if [[ $software_exit_code -eq 0 ]]; then
        log_info "$target_node 节点软件信息检测成功"
        software_check_result="成功"
    else
        log_warn "$target_node 节点软件信息检测失败"
        software_check_result="失败"
        # 软件信息检测失败不影响整体流程，继续执行
    fi

    log_info "$target_node 节点检测完成 (网络检测: $network_check_result, 硬件检测: $hardware_check_result, 系统信息检测: $system_check_result, 软件信息检测: $software_check_result)"

    return 0
}

run_detection() {
    log_info "开始执行检测任务..."

    local success_count=0
    local total_count=0

    # 遍历目标节点执行检测
    for node in $TARGET_NODES; do
        total_count=$((total_count + 1))

        echo ""
        log_info "========== 检测节点: $node =========="

        if execute_detection "$node"; then
            success_count=$((success_count + 1))
            log_info "$node 节点检测成功"
        else
            log_error "$node 节点检测失败"
        fi

        echo ""
    done

    # 输出检测结果统计
    log_info "========== 检测结果统计 =========="
    log_info "总节点数: $total_count"
    log_info "成功节点数: $success_count"
    log_info "失败节点数: $((total_count - success_count))"

    if [[ $success_count -eq $total_count ]]; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# 原始数据收集函数
# =============================================================================

# 收集硬件相关的原始命令输出
collect_raw_hardware_data() {
    local node="$1"
    local host="$2"
    local user="$3"
    local password="$4"
    local port="$5"

    local raw_hw_file="$RAW_DATA_DIR/hardware_${node}.txt"

    cat >"$raw_hw_file" <<'EOF'
# CPU信息
EOF

    if [[ "$node" == "n1" ]]; then
        # AFW3000设备使用show ver命令
        ssh_execute "$host" "$user" "$password" "$port" "show ver" 2>/dev/null | grep -E "(CPU|处理器)" >>"$raw_hw_file" 2>/dev/null || echo "无CPU信息" >>"$raw_hw_file"
    else
        # Linux系统使用标准命令
        echo "## lscpu输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "lscpu" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "lscpu命令执行失败" >>"$raw_hw_file"

        echo -e "\n# 内存信息" >>"$raw_hw_file"
        echo "## free -h输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "free -h" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "free命令执行失败" >>"$raw_hw_file"

        echo -e "\n## dmidecode -t memory输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "dmidecode -t memory 2>/dev/null" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "dmidecode命令执行失败" >>"$raw_hw_file"

        echo -e "\n# 存储信息" >>"$raw_hw_file"
        echo "## lsblk输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "lsblk" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "lsblk命令执行失败" >>"$raw_hw_file"

        echo -e "\n## df -h输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "df -h" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "df命令执行失败" >>"$raw_hw_file"
    fi
}

# 收集系统信息相关的原始命令输出
collect_raw_system_data() {
    local node="$1"
    local host="$2"
    local user="$3"
    local password="$4"
    local port="$5"

    local raw_sys_file="$RAW_DATA_DIR/system_${node}.txt"

    cat >"$raw_sys_file" <<'EOF'
# 系统版本信息
EOF

    if [[ "$node" == "n1" ]]; then
        # AFW3000设备
        echo "## show ver输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "show ver" 2>/dev/null >>"$raw_sys_file" || echo "show ver命令执行失败" >>"$raw_sys_file"
    else
        # Linux系统
        echo "## uname -a输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "uname -a" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "uname命令执行失败" >>"$raw_sys_file"

        echo -e "\n## /etc/os-release输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "cat /etc/os-release" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "/etc/os-release读取失败" >>"$raw_sys_file"

        echo -e "\n## uptime输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "uptime" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "uptime命令执行失败" >>"$raw_sys_file"
    fi
}

# 收集网络相关的原始命令输出
collect_raw_network_data() {
    local node="$1"
    local host="$2"
    local user="$3"
    local password="$4"
    local port="$5"

    local raw_net_file="$RAW_DATA_DIR/network_${node}.txt"

    cat >"$raw_net_file" <<'EOF'
# 网络接口信息
EOF

    if [[ "$node" == "n1" ]]; then
        # AFW3000设备
        echo "## show interface输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "show interface" 2>/dev/null >>"$raw_net_file" || echo "show interface命令执行失败" >>"$raw_net_file"
    else
        # Linux系统
        echo "## ip addr输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "ip addr" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "ip addr命令执行失败" >>"$raw_net_file"

        echo -e "\n## ping测试结果:" >>"$raw_net_file"
        ping -c 3 "$host" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "ping测试失败" >>"$raw_net_file"
    fi
}

# =============================================================================
# 报告生成
# =============================================================================

# 本地检测报告生成函数
generate_local_report() {
    local hostname=$(hostname)
    local local_ip=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}' 2>/dev/null || echo "127.0.0.1")

    cat >>"$REPORT_FILE" <<EOF
[本地主机 - $hostname ($local_ip)]
EOF

    # 检查硬件检测结果
    local temp_hardware_file="/tmp/afw3000_hardware_result_localhost_$$"
    local hardware_status="正常"
    if [[ -f "$temp_hardware_file" ]]; then
        local hw_exit_code=$(grep "HARDWARE_CHECK_EXIT_CODE=" "$temp_hardware_file" | cut -d'=' -f2)
        if [[ "$hw_exit_code" -ne 0 ]]; then
            hardware_status="异常"
        fi
    else
        hardware_status="未检测"
    fi

    # 检查系统检测结果
    local temp_system_file="/tmp/afw3000_system_result_localhost_$$"
    local system_status="正常"
    if [[ -f "$temp_system_file" ]]; then
        local sys_exit_code=$(grep "SYSTEM_CHECK_EXIT_CODE=" "$temp_system_file" | cut -d'=' -f2)
        if [[ "$sys_exit_code" -ne 0 ]]; then
            system_status="异常"
        fi
    else
        system_status="未检测"
    fi

    # 检查软件检测结果
    local temp_software_file="/tmp/afw3000_software_result_localhost_$$"
    local software_status="正常"
    if [[ -f "$temp_software_file" ]]; then
        local soft_exit_code=$(grep "SOFTWARE_CHECK_EXIT_CODE=" "$temp_software_file" | cut -d'=' -f2)
        if [[ "$soft_exit_code" -ne 0 ]]; then
            software_status="异常"
        fi
    else
        software_status="未检测"
    fi

    cat >>"$REPORT_FILE" <<EOF
├── 硬件信息：$hardware_status
│   └── 本地检测完成
├── 系统信息：$system_status
│   └── 本地检测完成
└── 软件信息：$software_status
    └── 本地检测完成

EOF

    # 清理临时文件
    rm -f "$temp_hardware_file" "$temp_system_file" "$temp_software_file" 2>/dev/null
}

generate_report() {

    local report_filename
    if [[ "$LOCAL_MODE" == "true" ]]; then
        report_filename="afw3000_local_check_${START_TIME}.txt"
    else
        report_filename="afw3000_check_${START_TIME}.txt"
    fi
    REPORT_FILE="$REPORT_DIR/$report_filename"

    # 确定检测范围描述
    local scope_desc
    if [[ "$LOCAL_MODE" == "true" ]]; then
        scope_desc="本地主机 ($(hostname))"
    else
        case "$TARGET_NODES" in
        "n1 n2 n3") scope_desc="所有节点 (N1, N2, N3)" ;;
        "n1") scope_desc="N1节点" ;;
        "n2") scope_desc="N2节点" ;;
        "n3") scope_desc="N3节点" ;;
        *) scope_desc="$TARGET_NODES" ;;
        esac
    fi

    # 开始生成报告
    cat >"$REPORT_FILE" <<EOF
========================================
AFW3000防火墙设备检测报告
========================================

检测时间：$(date +"%Y-%m-%d %H:%M:%S")
检测范围：$scope_desc
脚本版本：$SCRIPT_VERSION

EOF

    # 统计检测结果
    local total_nodes=0
    local success_nodes=0
    local failed_nodes=0

    if [[ "$LOCAL_MODE" == "true" ]]; then
        # 本地检测模式统计
        total_nodes=1
        # 检查本地检测是否成功（基于硬件检测结果）
        local temp_hardware_file="/tmp/afw3000_hardware_result_localhost_$$"
        if [[ -f "$temp_hardware_file" ]]; then
            local exit_code=$(grep "HARDWARE_CHECK_EXIT_CODE=" "$temp_hardware_file" | cut -d'=' -f2)
            if [[ "$exit_code" -eq 0 ]]; then
                success_nodes=1
            else
                failed_nodes=1
            fi
        else
            failed_nodes=1
        fi
    else
        # 远程检测模式统计
        for node in $TARGET_NODES; do
            total_nodes=$((total_nodes + 1))
            local temp_result_file="/tmp/afw3000_network_result_${node}_$$"
            if [[ -f "$temp_result_file" ]]; then
                local exit_code=$(grep "NETWORK_CHECK_EXIT_CODE=" "$temp_result_file" | cut -d'=' -f2)
                if [[ "$exit_code" -eq 0 ]]; then
                    success_nodes=$((success_nodes + 1))
                else
                    failed_nodes=$((failed_nodes + 1))
                fi
            else
                failed_nodes=$((failed_nodes + 1))
            fi
        done
    fi

    # 计算成功率
    local success_rate=0
    if [[ $total_nodes -gt 0 ]]; then
        success_rate=$(echo "scale=1; $success_nodes * 100 / $total_nodes" | bc 2>/dev/null || echo "$(($success_nodes * 100 / $total_nodes))")
    fi

    # 生成设备检测概况
    cat >>"$REPORT_FILE" <<EOF
========================================
设备检测概况
========================================

总节点数：$total_nodes
检测成功：$success_nodes
检测失败：$failed_nodes
检测成功率：${success_rate}%

========================================
设备详细检测结果
========================================

EOF

    # 为每个目标节点生成检测结果
    if [[ "$LOCAL_MODE" == "true" ]]; then
        # 本地检测模式报告生成
        generate_local_report
    else
        # 远程检测模式报告生成
        for node in $TARGET_NODES; do
            local temp_result_file="/tmp/afw3000_network_result_${node}_$$"

            # 获取节点配置信息
            local node_config
            node_config=$(get_device_config "$node")
            local host user password port backup_user backup_password
            read -r host user password port backup_user backup_password <<<"$node_config"

            cat >>"$REPORT_FILE" <<EOF
[${node^^}节点 - $host]
EOF

            # 检查是否有网络检测结果
            if [[ -f "$temp_result_file" ]]; then

                # 读取网络检测结果
                local exit_code=$(grep "NETWORK_CHECK_EXIT_CODE=" "$temp_result_file" | cut -d'=' -f2)
                local output_content=$(sed -n '/NETWORK_CHECK_OUTPUT_START/,/NETWORK_CHECK_OUTPUT_END/p' "$temp_result_file" | sed '1d;$d')

                if [[ "$exit_code" -eq 0 ]]; then

                    # 解析网络检测结果
                    local network_status="正常"
                    local port_results=""

                    # 从输出中提取端口检测结果
                    local port_lines=()
                    while IFS= read -r line; do
                        if [[ "$line" =~ 端口\ ([0-9]+):\ (.+) ]]; then
                            local port_num="${BASH_REMATCH[1]}"
                            local port_status="${BASH_REMATCH[2]}"
                            if [[ "$port_status" == *"开放"* ]]; then
                                port_lines+=("│   ├── 端口${port_num}：开放")
                            else
                                port_lines+=("│   ├── 端口${port_num}：关闭")
                            fi
                        fi
                    done <<<"$output_content"

                    # 构建端口结果，最后一个使用└──
                    for i in "${!port_lines[@]}"; do
                        if [[ $i -eq $((${#port_lines[@]} - 1)) ]]; then
                            # 最后一个端口使用└──
                            port_results+="${port_lines[i]/├──/└──}"$'\n'
                        else
                            port_results+="${port_lines[i]}"$'\n'
                        fi
                    done

                    # 如果没有提取到端口信息，使用默认格式
                    if [[ -z "$port_results" ]]; then
                        port_results="│   └── SSH连接：成功"
                    else
                        # 将最后一个├──改为└──，并确保末尾没有换行
                        port_results=$(echo "$port_results" | sed '$s/├──/└──/' | sed '$s/$//')
                    fi

                    # 检查硬件检测结果
                    local temp_hardware_file="/tmp/afw3000_hardware_result_${node}_$$"
                    local hardware_status="模块开发中"
                    local hardware_details=""

                    if [[ -f "$temp_hardware_file" ]]; then
                        local hw_exit_code=$(grep "HARDWARE_CHECK_EXIT_CODE=" "$temp_hardware_file" | cut -d'=' -f2)
                        if [[ "$hw_exit_code" -eq 0 ]]; then
                            hardware_status="正常"
                            local hw_output=$(sed -n '/HARDWARE_CHECK_OUTPUT_START/,/HARDWARE_CHECK_OUTPUT_END/p' "$temp_hardware_file" | sed '1d;$d')

                            # 根据设备类型生成硬件详细信息
                            if [[ "$node" == "n1" ]]; then
                                # AFW3000防火墙设备的硬件信息格式 - 从硬件检测输出中提取实际信息

                                # 提取主板信息
                                local mb_platform=$(echo "$hw_output" | grep "平台:" | cut -d':' -f2 | xargs)
                                local mb_model=$(echo "$hw_output" | grep "型号:" | head -1 | cut -d':' -f2 | xargs)
                                local mb_product=$(echo "$hw_output" | grep "产品:" | cut -d':' -f2 | xargs)

                                # 提取CPU信息
                                local cpu_model=$(echo "$hw_output" | grep "型号:" | sed -n '2p' | cut -d':' -f2 | xargs)
                                local cpu_cores=$(echo "$hw_output" | grep "核数:" | cut -d':' -f2 | xargs)
                                local cpu_freq=$(echo "$hw_output" | grep "频率:" | cut -d':' -f2 | xargs)

                                # 提取内存信息
                                local mem_total=$(echo "$hw_output" | grep "总容量：" | head -1 | cut -d'：' -f2 | xargs)

                                # 提取内存条列表详细信息
                                local mem_details=""
                                local in_mem_list=false
                                local mem_items=()

                                while IFS= read -r line; do
                                    if [[ "$line" =~ "内存条列表:" ]]; then
                                        in_mem_list=true
                                        continue
                                    fi
                                    if [[ "$in_mem_list" == true ]]; then
                                        if [[ "$line" =~ ^[[:space:]]*[├└]──.*内存条[0-9] ]] || [[ "$line" =~ ^[[:space:]]*[├└]──.*无内存条信息 ]]; then
                                            # 提取内容，去掉原有的树状符号
                                            local item_content=$(echo "$line" | sed 's/^[[:space:]]*[├└]──[[:space:]]*//')
                                            mem_items+=("$item_content")
                                        elif [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" =~ "========" ]]; then
                                            # 遇到空行或分隔符，结束内存列表提取
                                            break
                                        fi
                                    fi
                                done <<<"$hw_output"

                                # 格式化内存条列表，使用正确的树状结构
                                if [[ ${#mem_items[@]} -gt 0 ]]; then
                                    for i in "${!mem_items[@]}"; do
                                        if [[ $i -eq $((${#mem_items[@]} - 1)) ]]; then
                                            mem_details+="│   |       └── ${mem_items[i]}"$'\n'
                                        else
                                            mem_details+="│   |       ├── ${mem_items[i]}"$'\n'
                                        fi
                                    done
                                else
                                    mem_details="│   |       └── 无内存条信息"$'\n'
                                fi

                                # 提取存储信息
                                local storage_total=$(echo "$hw_output" | grep "总容量：" | tail -1 | cut -d'：' -f2 | xargs)

                                # 提取硬盘列表详细信息
                                local storage_details=""
                                local in_storage_list=false
                                local storage_items=()

                                while IFS= read -r line; do
                                    if [[ "$line" =~ "硬盘列表:" ]]; then
                                        in_storage_list=true
                                        continue
                                    fi
                                    if [[ "$in_storage_list" == true ]]; then
                                        if [[ "$line" =~ ^[[:space:]]*[├└]──.*硬盘[0-9] ]] || [[ "$line" =~ ^[[:space:]]*[├└]──.*无存储设备信息 ]] || [[ "$line" =~ ^[[:space:]]*[├└]──.*Flash存储 ]] || [[ "$line" =~ ^[[:space:]]*[├└]──.*磁盘存储 ]]; then
                                            # 提取内容，去掉原有的树状符号
                                            local item_content=$(echo "$line" | sed 's/^[[:space:]]*[├└]──[[:space:]]*//')
                                            storage_items+=("$item_content")
                                        elif [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" =~ "========" ]]; then
                                            # 遇到空行或分隔符，结束存储列表提取
                                            break
                                        fi
                                    fi
                                done <<<"$hw_output"

                                # 格式化硬盘列表，使用正确的树状结构
                                if [[ ${#storage_items[@]} -gt 0 ]]; then
                                    for i in "${!storage_items[@]}"; do
                                        if [[ $i -eq $((${#storage_items[@]} - 1)) ]]; then
                                            storage_details+="│   │       └── ${storage_items[i]}"$'\n'
                                        else
                                            storage_details+="│   │       ├── ${storage_items[i]}"$'\n'
                                        fi
                                    done
                                else
                                    storage_details="│   │       └── 无存储设备信息"$'\n'
                                fi

                                # 提取网口信息
                                local nic_total=$(echo "$hw_output" | grep "网口总数:" | cut -d':' -f2 | xargs)

                                # 提取网口列表详细信息
                                local nic_details=""
                                local in_nic_list=false
                                while IFS= read -r line; do
                                    if [[ "$line" =~ "网口列表:" ]]; then
                                        in_nic_list=true
                                        continue
                                    fi
                                    if [[ "$in_nic_list" == true ]]; then
                                        if [[ "$line" =~ ^[[:space:]]*├──.*ge[0-9] ]] || [[ "$line" =~ ^[[:space:]]*└──.*ge[0-9] ]] || [[ "$line" =~ ^[[:space:]]*└──.*未配置 ]]; then
                                            # 转换为报告格式的缩进
                                            local formatted_line=$(echo "$line" | sed 's/^[[:space:]]*/│           /')
                                            nic_details+="$formatted_line"$'\n'
                                        elif [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" =~ "========" ]]; then
                                            # 遇到空行或分隔符，结束网口列表提取
                                            break
                                        fi
                                    fi
                                done <<<"$hw_output"

                                # 如果没有提取到网口详细信息，使用默认显示
                                if [[ -z "$nic_details" ]]; then
                                    nic_details="│           └── 无网口详细信息"$'\n'
                                fi

                                hardware_details="│   ├── 主板：
│   │   ├── 平台：${mb_platform:-"-"}
│   │   ├── 型号：${mb_model:-"-"}
│   │   └── 产品：${mb_product:-"-"}
│   ├── CPU：
│   │   ├── 型号：${cpu_model:-"-"}
│   │   ├── 核数：${cpu_cores:-"-"}
│   │   └── 频率：${cpu_freq:-"-"}
│   ├── 内存：
│   │   ├── 总容量：${mem_total:-"-"}
│   │   └── 内存条列表：
${mem_details%$'\n'}
│   ├── 存储：
│   │   ├── 总容量：${storage_total:-"-"}
│   │   └── 硬盘列表：
${storage_details%$'\n'}
│   └── 网口：
│       ├── 网口总数：${nic_total:-"-"}
│       └── 网口列表：
${nic_details%$'\n'}"
                            else
                                # Linux系统的硬件信息格式 - 从硬件检测输出中提取实际信息
                                local cpu_model="-"
                                local cpu_cores="-"
                                local cpu_freq="-"

                                # 从硬件检测输出中提取CPU信息
                                if echo "$hw_output" | grep -q "型号:"; then
                                    cpu_model=$(echo "$hw_output" | grep "型号:" | head -1 | cut -d':' -f2 | xargs)
                                fi
                                if echo "$hw_output" | grep -q "核数:"; then
                                    cpu_cores=$(echo "$hw_output" | grep "核数:" | head -1 | cut -d':' -f2 | xargs)
                                fi
                                if echo "$hw_output" | grep -q "频率:"; then
                                    cpu_freq=$(echo "$hw_output" | grep "频率:" | head -1 | cut -d':' -f2 | xargs)
                                fi

                                # 提取内存信息
                                local mem_total=$(echo "$hw_output" | grep "总容量：" | head -1 | cut -d'：' -f2 | xargs)

                                # 提取内存条列表详细信息
                                local mem_details=""
                                local in_mem_list=false
                                local mem_items=()

                                while IFS= read -r line; do
                                    if [[ "$line" =~ "内存条列表:" ]]; then
                                        in_mem_list=true
                                        continue
                                    fi
                                    if [[ "$in_mem_list" == true ]]; then
                                        if [[ "$line" =~ ^[[:space:]]*[├└]──.*内存条[0-9] ]] || [[ "$line" =~ ^[[:space:]]*[├└]──.*无内存条信息 ]]; then
                                            # 提取内容，去掉原有的树状符号
                                            local item_content=$(echo "$line" | sed 's/^[[:space:]]*[├└]──[[:space:]]*//')
                                            mem_items+=("$item_content")
                                        elif [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" =~ "========" ]]; then
                                            # 遇到空行或分隔符，结束内存列表提取
                                            break
                                        fi
                                    fi
                                done <<<"$hw_output"

                                # 格式化内存条列表，使用正确的树状结构
                                if [[ ${#mem_items[@]} -gt 0 ]]; then
                                    for i in "${!mem_items[@]}"; do
                                        if [[ $i -eq $((${#mem_items[@]} - 1)) ]]; then
                                            mem_details+="│   |       └── ${mem_items[i]}"$'\n'
                                        else
                                            mem_details+="│   |       ├── ${mem_items[i]}"$'\n'
                                        fi
                                    done
                                else
                                    mem_details="│   |       └── 无内存条信息"$'\n'
                                fi

                                # 提取存储信息
                                local storage_total=$(echo "$hw_output" | grep "总容量：" | tail -1 | cut -d'：' -f2 | xargs)

                                # 提取硬盘列表详细信息
                                local storage_details=""
                                local in_storage_list=false
                                local storage_items=()

                                while IFS= read -r line; do
                                    if [[ "$line" =~ "硬盘列表:" ]]; then
                                        in_storage_list=true
                                        continue
                                    fi
                                    if [[ "$in_storage_list" == true ]]; then
                                        if [[ "$line" =~ ^[[:space:]]*[├└]──.*硬盘[0-9] ]] || [[ "$line" =~ ^[[:space:]]*[├└]──.*无硬盘信息 ]]; then
                                            # 提取内容，去掉原有的树状符号
                                            local item_content=$(echo "$line" | sed 's/^[[:space:]]*[├└]──[[:space:]]*//')
                                            storage_items+=("$item_content")
                                        elif [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" =~ "========" ]]; then
                                            # 遇到空行或分隔符，结束存储列表提取
                                            break
                                        fi
                                    fi
                                done <<<"$hw_output"

                                # 格式化硬盘列表，使用正确的树状结构
                                if [[ ${#storage_items[@]} -gt 0 ]]; then
                                    for i in "${!storage_items[@]}"; do
                                        if [[ $i -eq $((${#storage_items[@]} - 1)) ]]; then
                                            storage_details+="│   │       └── ${storage_items[i]}"$'\n'
                                        else
                                            storage_details+="│   │       ├── ${storage_items[i]}"$'\n'
                                        fi
                                    done
                                else
                                    storage_details="│   │       └── 无存储设备信息"$'\n'
                                fi

                                # 提取网口信息
                                local nic_total=$(echo "$hw_output" | grep "网口总数:" | cut -d':' -f2 | xargs)

                                # 提取网口列表详细信息
                                local nic_details=""
                                local in_nic_list=false
                                while IFS= read -r line; do
                                    if [[ "$line" =~ "网口列表:" ]]; then
                                        in_nic_list=true
                                        continue
                                    fi
                                    if [[ "$in_nic_list" == true ]]; then
                                        if [[ "$line" =~ ^[[:space:]]*├──.*: ]] || [[ "$line" =~ ^[[:space:]]*└──.*: ]]; then
                                            # 转换为报告格式的缩进
                                            local formatted_line=$(echo "$line" | sed 's/^[[:space:]]*/│           /')
                                            nic_details+="$formatted_line"$'\n'
                                        elif [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" =~ "========" ]]; then
                                            # 遇到空行或分隔符，结束网口列表提取
                                            break
                                        fi
                                    fi
                                done <<<"$hw_output"

                                # 如果没有提取到网口详细信息，使用默认显示
                                if [[ -z "$nic_details" ]]; then
                                    nic_details="│           └── 无网口详细信息"$'\n'
                                fi

                                hardware_details="│   ├── 主板：
│   │   ├── 型号：Custom ARM Board
│   │   ├── 制造商：Unknown
│   │   └── BIOS版本：U-Boot 2020.01
│   ├── CPU：
│   │   ├── 型号：$cpu_model
│   │   ├── 核数：$cpu_cores
│   │   └── 频率：$cpu_freq
│   ├── 内存：
│   │   ├── 总容量：${mem_total:-"-"}
│   │   └── 内存条列表：
${mem_details%$'\n'}
│   ├── 存储：
│   │   ├── 总容量：${storage_total:-"-"}
│   │   └── 硬盘列表：
${storage_details%$'\n'}
│   └── 网口：
│       ├── 网口总数：${nic_total:-"-"}
│       └── 网口列表：
${nic_details%$'\n'}"
                            fi
                        else
                            hardware_status="异常"
                            hardware_details="│   └── 检测失败：硬件信息获取失败"
                        fi
                    else
                        hardware_details="│   └── 状态：模块开发中"
                    fi

                    # 检查系统信息检测结果
                    local temp_system_file="/tmp/afw3000_system_result_${node}_$$"
                    local system_status="模块开发中"
                    local system_details=""

                    if [[ -f "$temp_system_file" ]]; then
                        local sys_exit_code=$(grep "SYSTEM_CHECK_EXIT_CODE=" "$temp_system_file" | cut -d'=' -f2)
                        if [[ "$sys_exit_code" -eq 0 ]]; then
                            system_status="正常"
                            local sys_output=$(sed -n '/SYSTEM_CHECK_OUTPUT_START/,/SYSTEM_CHECK_OUTPUT_END/p' "$temp_system_file" | sed '1d;$d')

                            # 根据设备类型生成系统详细信息
                            if [[ "$node" == "n1" ]]; then
                                # AFW3000防火墙设备的系统信息格式
                                local hostname_name=$(echo "$sys_output" | grep "主机名称:" | cut -d':' -f2 | xargs)
                                local fw_version=$(echo "$sys_output" | grep "防火墙版本:" | cut -d':' -f2 | xargs)
                                local firmware_version=$(echo "$sys_output" | grep "固件版本:" | cut -d':' -f2 | xargs)
                                local platform=$(echo "$sys_output" | grep "平台:" | cut -d':' -f2 | xargs)
                                local load_avg=$(echo "$sys_output" | grep "系统负载:" | cut -d':' -f2 | xargs)
                                local mem_usage=$(echo "$sys_output" | grep "内存使用率:" | cut -d':' -f2 | xargs)
                                local disk_usage=$(echo "$sys_output" | grep "硬盘使用率:" | cut -d':' -f2 | xargs)
                                local uptime=$(echo "$sys_output" | grep "运行时间:" | cut -d':' -f2 | xargs)
                                local software_sn=$(echo "$sys_output" | grep "Software S/N:" | cut -d':' -f2 | xargs)

                                system_details="│   ├── 系统版本：
│   │   ├── 主机名称：${hostname_name:-"-"}
│   │   ├── 防火墙版本：${fw_version:-"-"}
│   │   ├── 固件版本：${firmware_version:-"-"}
│   │   └── 平台：${platform:-"-"}
│   ├── 系统状态：
│   │   ├── 系统负载：${load_avg:-"-"}
│   │   ├── 内存使用率：${mem_usage:-"-"}
│   │   ├── 硬盘使用率：${disk_usage:-"-"}
│   │   └── 运行时间：${uptime:-"-"}
│   └── 软件序列号：
│       └── Software S/N：${software_sn:-"-"}"
                            else
                                # Linux系统的系统信息格式
                                local hostname_name=$(echo "$sys_output" | grep "主机名称:" | cut -d':' -f2 | xargs)
                                local distro_name=$(echo "$sys_output" | grep "发行版本:" | cut -d':' -f2 | xargs)
                                local kernel_version=$(echo "$sys_output" | grep "内核版本:" | cut -d':' -f2 | xargs)
                                local architecture=$(echo "$sys_output" | grep "系统架构:" | cut -d':' -f2 | xargs)
                                local load_avg=$(echo "$sys_output" | grep "系统负载:" | cut -d':' -f2 | xargs)
                                local mem_usage=$(echo "$sys_output" | grep "内存使用率:" | cut -d':' -f2 | xargs)
                                local disk_usage=$(echo "$sys_output" | grep "硬盘使用率:" | cut -d':' -f2 | xargs)
                                local uptime=$(echo "$sys_output" | grep "运行时间:" | tail -1 | cut -d':' -f2 | xargs)

                                system_details="│   ├── 系统版本：
│   │   ├── 主机名称：${hostname_name:-"-"}
│   │   ├── 发行版本：${distro_name:-"-"}
│   │   ├── 内核版本：${kernel_version:-"-"}
│   │   └── 系统架构：${architecture:-"-"}
│   └── 系统状态：
│       ├── 系统负载：${load_avg:-"-"}
│       ├── 内存使用率：${mem_usage:-"-"}
│       ├── 硬盘使用率：${disk_usage:-"-"}
│       └── 运行时间：${uptime:-"-"}"
                            fi
                        else
                            system_status="异常"
                            system_details="│   └── 检测失败：系统信息获取失败"
                        fi
                    else
                        system_details="│   └── 状态：模块开发中"
                    fi

                    # 检查软件信息检测结果
                    local temp_software_file="/tmp/afw3000_software_result_${node}_$$"
                    local software_status="模块开发中"
                    local software_details=""

                    if [[ -f "$temp_software_file" ]]; then
                        local soft_exit_code=$(grep "SOFTWARE_CHECK_EXIT_CODE=" "$temp_software_file" | cut -d'=' -f2)
                        if [[ "$soft_exit_code" -eq 0 ]]; then
                            software_status="正常"
                            local soft_output=$(sed -n '/SOFTWARE_CHECK_OUTPUT_START/,/SOFTWARE_CHECK_OUTPUT_END/p' "$temp_software_file" | sed '1d;$d')

                            # 根据设备类型生成软件详细信息
                            if [[ "$node" == "n1" ]]; then
                                # AFW3000防火墙设备的软件信息格式
                                local web_status=$(echo "$soft_output" | grep "Web服务状态:" | cut -d':' -f2 | xargs)
                                local web_address=$(echo "$soft_output" | grep "访问地址:" | cut -d':' -f2- | xargs)
                                local response_time=$(echo "$soft_output" | grep "响应时间:" | cut -d':' -f2 | xargs)
                                local login_page=$(echo "$soft_output" | grep "登录页面:" | cut -d':' -f2 | xargs)

                                software_details="    └── Web服务：
        ├── 服务状态：${web_status:-"-"}
        ├── 访问地址：${web_address:-"-"}
        ├── 响应时间：${response_time:-"-"}
        └── 登录页面：${login_page:-"-"}"
                            else
                                # Linux系统的软件信息格式
                                local service_count=$(echo "$soft_output" | grep "检测服务:" | tail -1 | awk '{print $2}')
                                local running_count=$(echo "$soft_output" | grep "运行中:" | tail -1 | awk '{print $2}')
                                local not_installed_count=$(echo "$soft_output" | grep "未安装:" | tail -1 | awk '{print $2}')

                                # 提取三个类目的服务列表，包含友好名称
                                local security_suite_success=()
                                local security_suite_failed=()
                                local security_hardening_success=()
                                local security_hardening_failed=()
                                local other_software_success=()
                                local other_software_failed=()

                                local current_service=""
                                local current_service_name=""
                                local current_service_category=""
                                local in_service_block=false

                                while IFS= read -r line; do
                                    # 解析格式：检测服务: watchdog (Watchdog服务|security_suite|系统看门狗服务)
                                    if [[ "$line" =~ 检测服务:[[:space:]]*([^[:space:]]+)[[:space:]]*\(([^|]+)\|([^|]+)\|.*\) ]]; then
                                        current_service="${BASH_REMATCH[1]}"
                                        current_service_name="${BASH_REMATCH[2]}"
                                        current_service_category="${BASH_REMATCH[3]}"
                                        in_service_block=true
                                    elif [[ "$line" =~ 状态:[[:space:]]*运行中 ]] && [[ -n "$current_service" ]] && [[ "$in_service_block" == true ]]; then
                                        case "$current_service_category" in
                                        "security_suite")
                                            security_suite_success+=("$current_service_name")
                                            ;;
                                        "security_hardening")
                                            security_hardening_success+=("$current_service_name")
                                            ;;
                                        "other_software")
                                            other_software_success+=("$current_service_name")
                                            ;;
                                        esac
                                        current_service=""
                                        current_service_name=""
                                        current_service_category=""
                                        in_service_block=false
                                    elif [[ "$line" =~ 状态:[[:space:]]*未安装 ]] && [[ -n "$current_service" ]] && [[ "$in_service_block" == true ]]; then
                                        case "$current_service_category" in
                                        "security_suite")
                                            security_suite_failed+=("$current_service_name")
                                            ;;
                                        "security_hardening")
                                            security_hardening_failed+=("$current_service_name")
                                            ;;
                                        "other_software")
                                            other_software_failed+=("$current_service_name")
                                            ;;
                                        esac
                                        current_service=""
                                        current_service_name=""
                                        current_service_category=""
                                        in_service_block=false
                                    fi
                                done <<<"$soft_output"

                                # 生成安全套件类目的树状列表
                                local security_suite_details=""
                                if [[ ${#security_suite_success[@]} -gt 0 || ${#security_suite_failed[@]} -gt 0 ]]; then
                                    security_suite_details+="    ├── 安全套件："$'\n'
                                    if [[ ${#security_suite_success[@]} -gt 0 ]]; then
                                        security_suite_details+="    │   ├── 安装成功："$'\n'
                                        for i in "${!security_suite_success[@]}"; do
                                            if [[ $i -eq $((${#security_suite_success[@]} - 1)) && ${#security_suite_failed[@]} -eq 0 ]]; then
                                                security_suite_details+="    │   └── ${security_suite_success[i]}"$'\n'
                                            else
                                                security_suite_details+="    │   │   ├── ${security_suite_success[i]}"$'\n'
                                            fi
                                        done
                                    fi
                                    if [[ ${#security_suite_failed[@]} -gt 0 ]]; then
                                        security_suite_details+="    │   └── 安装失败："$'\n'
                                        for i in "${!security_suite_failed[@]}"; do
                                            if [[ $i -eq $((${#security_suite_failed[@]} - 1)) ]]; then
                                                security_suite_details+="    │       └── ${security_suite_failed[i]}"$'\n'
                                            else
                                                security_suite_details+="    │       ├── ${security_suite_failed[i]}"$'\n'
                                            fi
                                        done
                                    fi
                                fi

                                # 生成安全加固类目的树状列表
                                local security_hardening_details=""
                                if [[ ${#security_hardening_success[@]} -gt 0 || ${#security_hardening_failed[@]} -gt 0 ]]; then
                                    security_hardening_details+="    ├── 安全加固："$'\n'
                                    if [[ ${#security_hardening_success[@]} -gt 0 ]]; then
                                        security_hardening_details+="    │   ├── 安装成功："$'\n'
                                        for i in "${!security_hardening_success[@]}"; do
                                            if [[ $i -eq $((${#security_hardening_success[@]} - 1)) && ${#security_hardening_failed[@]} -eq 0 ]]; then
                                                security_hardening_details+="    │   └── ${security_hardening_success[i]}"$'\n'
                                            else
                                                security_hardening_details+="    │   │   ├── ${security_hardening_success[i]}"$'\n'
                                            fi
                                        done
                                    fi
                                    if [[ ${#security_hardening_failed[@]} -gt 0 ]]; then
                                        security_hardening_details+="    │   └── 安装失败："$'\n'
                                        for i in "${!security_hardening_failed[@]}"; do
                                            if [[ $i -eq $((${#security_hardening_failed[@]} - 1)) ]]; then
                                                security_hardening_details+="    │       └── ${security_hardening_failed[i]}"$'\n'
                                            else
                                                security_hardening_details+="    │       ├── ${security_hardening_failed[i]}"$'\n'
                                            fi
                                        done
                                    fi
                                fi

                                # 生成其他软件类目的树状列表
                                local other_software_details=""
                                if [[ ${#other_software_success[@]} -gt 0 || ${#other_software_failed[@]} -gt 0 ]]; then
                                    other_software_details+="    └── 其他软件："$'\n'
                                    if [[ ${#other_software_success[@]} -gt 0 ]]; then
                                        other_software_details+="        ├── 安装成功："$'\n'
                                        for i in "${!other_software_success[@]}"; do
                                            if [[ $i -eq $((${#other_software_success[@]} - 1)) && ${#other_software_failed[@]} -eq 0 ]]; then
                                                other_software_details+="        └── ${other_software_success[i]}"$'\n'
                                            else
                                                other_software_details+="        │   ├── ${other_software_success[i]}"$'\n'
                                            fi
                                        done
                                    fi
                                    if [[ ${#other_software_failed[@]} -gt 0 ]]; then
                                        other_software_details+="        └── 安装失败："$'\n'
                                        for i in "${!other_software_failed[@]}"; do
                                            if [[ $i -eq $((${#other_software_failed[@]} - 1)) ]]; then
                                                other_software_details+="            └── ${other_software_failed[i]}"$'\n'
                                            else
                                                other_software_details+="            ├── ${other_software_failed[i]}"$'\n'
                                            fi
                                        done
                                    fi
                                fi

                                # 移除最后的换行符
                                security_suite_details="${security_suite_details%$'\n'}"
                                security_hardening_details="${security_hardening_details%$'\n'}"
                                other_software_details="${other_software_details%$'\n'}"

                                software_details="    ├── 服务统计：
    │   ├── 检测服务：${service_count:-"-"}
    │   ├── 安装成功：${running_count:-"-"}
    │   └── 安装失败：${not_installed_count:-"-"}
$security_suite_details
$security_hardening_details
$other_software_details"
                            fi
                        else
                            software_status="异常"
                            software_details="│   └── 检测失败：软件信息获取失败"
                        fi
                    else
                        software_details="│   └── 状态：模块开发中"
                    fi

                    cat >>"$REPORT_FILE" <<EOF
├── 网络检测：$network_status
$port_results
├── 硬件信息：$hardware_status
$hardware_details
├── 系统信息：$system_status
$system_details
└── 软件信息：$software_status
$software_details

EOF
                else
                    cat >>"$REPORT_FILE" <<EOF
├── 网络检测：异常
│   └── 连接失败：无法建立网络连接
├── 硬件信息：跳过
│   └── 原因：网络检测失败
├── 系统信息：跳过
│   └── 原因：网络检测失败
└── 软件信息：跳过
    └── 原因：网络检测失败

EOF
                fi
            else
                cat >>"$REPORT_FILE" <<EOF
├── 网络检测：异常
│   └── 连接失败：检测未执行
├── 硬件信息：跳过
│   └── 原因：网络检测失败
├── 系统信息：跳过
│   └── 原因：网络检测失败
└── 软件信息：跳过
    └── 原因：网络检测失败

EOF
            fi

            # 收集核心命令的原始输出（用于后续附录生成）
            mkdir -p "$RAW_DATA_DIR"

            # 获取节点配置信息
            local node_config
            node_config=$(get_device_config "$node")
            local host user password port backup_user backup_password
            read -r host user password port backup_user backup_password <<<"$node_config"

            # 收集硬件相关的原始命令输出
            collect_raw_hardware_data "$node" "$host" "$user" "$password" "$port"

            # 收集系统信息相关的原始命令输出
            collect_raw_system_data "$node" "$host" "$user" "$password" "$port"

            # 收集网络相关的原始命令输出
            collect_raw_network_data "$node" "$host" "$user" "$password" "$port"

            # 清理临时文件
            rm -f "$temp_result_file"
            rm -f "/tmp/afw3000_hardware_result_${node}_$$"
            rm -f "/tmp/afw3000_system_result_${node}_$$"
            rm -f "/tmp/afw3000_software_result_${node}_$$"
        done
    fi

    # 添加原始检测数据附录
    cat >>"$REPORT_FILE" <<EOF

================================================================================
原始命令输出附录
================================================================================

本附录包含获取各配置指标的原始命令执行结果，供技术分析和对照验证使用。
数据按硬件配置、系统信息、网络配置分类展示，去除了检测流程信息，专注于核心命令输出。

EOF

    # 为每个节点添加原始检测数据
    for node in $TARGET_NODES; do
        cat >>"$REPORT_FILE" <<EOF
[${node^^}节点原始命令输出]
========================================

硬件配置原始数据
--------------
$(cat "$RAW_DATA_DIR/hardware_${node}.txt" 2>/dev/null || echo "无硬件配置数据")

系统信息原始数据
--------------
$(cat "$RAW_DATA_DIR/system_${node}.txt" 2>/dev/null || echo "无系统信息数据")

网络配置原始数据
--------------
$(cat "$RAW_DATA_DIR/network_${node}.txt" 2>/dev/null || echo "无网络配置数据")

========================================

EOF
    done

    # 清理原始数据临时目录
    rm -rf "$RAW_DATA_DIR" 2>/dev/null

    cat >>"$REPORT_FILE" <<EOF
================================================================================
附录结束
================================================================================

报告生成时间：$(date +"%Y-%m-%d %H:%M:%S")
EOF

    log_info "检测报告已生成: $REPORT_FILE"
}

# =============================================================================
# 清理和退出
# =============================================================================

cleanup_and_exit() {
    local exit_code=$1

    log_info "清理检测环境..."

    # 清理SSH连接
    ssh_cleanup

    # 关闭调试模式
    set +x

    log_info "检测任务完成"

    exit $exit_code
}

# =============================================================================
# 主函数
# =============================================================================

main() {

    # 解析命令行参数
    parse_arguments "$@"

    # 初始化环境
    initialize_environment

    # 执行检测，生成报告
    if run_detection; then
        generate_report
        cleanup_and_exit 0
    else
        generate_report
        cleanup_and_exit 1
    fi
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 设置错误处理
set -e
trap 'cleanup_and_exit 1' ERR INT TERM

# 调用主函数
main "$@"
